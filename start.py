#!/usr/bin/env python3
"""
    DisplayHelper Launcher
    Copyright (C) 2025 Development Team
    
    Simple launcher script for the DisplayHelper system.
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """Launch the DisplayHelper system."""
    # Get the directory containing this script
    script_dir = Path(__file__).parent
    src_dir = script_dir / "src"
    main_script = src_dir / "main.py"
    
    # Check if main script exists
    if not main_script.exists():
        print(f"Error: Main script not found at {main_script}")
        return 1
    
    # Change to src directory and run the main script
    try:
        os.chdir(src_dir)
        result = subprocess.run([sys.executable, "main.py"], 
                              capture_output=False, 
                              text=True)
        return result.returncode
    except KeyboardInterrupt:
        print("\nApplication terminated by user.")
        return 0
    except Exception as e:
        print(f"Error launching application: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
