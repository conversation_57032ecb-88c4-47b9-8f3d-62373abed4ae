; https://github.com/vike256/Unibot
; See Unibot Wiki for reference

[aim]
; winapi
; interception_driver
; microcontroller_serial
; microcontroller_socket
bot_input_type = winapi
screen_center_offset = 0
aim_smoothing_factor = 0.0
speed = 1.0
y_speed_multiplier = 1.0
aim_height = 0.5

[communication]
; IF USING TYPE SOCKET
microcontroller_ip = 0.0.0.0
microcontroller_port = 50256

; IF USING TYPE SERIAL
com_port = 1

[screen]
group_close_target_blobs_threshold = 3, 3

; HSV color range for target detection
;upper_color = 63, 255, 255
;lower_color = 58, 210, 80
upper_color = 115, 255, 255
lower_color = 95, 150, 150

capture_fov_x = 256
capture_fov_y = 256
aim_fov_x = 256
aim_fov_y = 256
max_loops_per_sec = 60
auto_detect_resolution = true
resolution_x = 1920
resolution_y = 1080

[recoil]
mode = move
recoil_x = 0.0
recoil_y = 0.0
max_offset = 100
recover = 0.0

[trigger]
; Base delay for triggerbot
trigger_delay = 0
; Additional random delay, does nothing if trigger_delay is 0
trigger_randomization = 30
trigger_threshold = 8

[rapid_fire]
; Maximum clicks per second sent by Unibot, used for triggerbot and rapidfire
target_cps = 10

[key_binds]
; https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes
; F1
key_reload_config = 0x70
; F2
key_toggle_aim = 0x71
; F3
key_toggle_recoil = 0x72
; F4
key_exit = 0x73
; Mouse 4
key_trigger = 0x06
; Mouse5
key_rapid_fire = 0x05
; Mouse1 & Mouse2
aim_keys = 0x01, 0x02

[debug]
; Show a debug screen
enabled = true
; Refresh debug screen even if cheats are off
always_on = true
; 'game' shows actual screen, 'mask' shows what opencv sees
display_mode = mask
