"""
    Unibot, an open-source colorbot.
    Copyright (C) 2025 vike256

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import numpy as np

from cheats import Cheats
from mouse import get_mouse_implementation
from screen import Screen
from utils import Utils


class Unibot:
    def run(self):
        self.print_license()  # 打印版权信息
        while True:  # 主循环，支持配置热重载
            # 记录循环开始时间，用于计算每帧耗时
            start_time = time.time()

            utils = Utils()  # 工具类，处理配置和按键绑定
            config = utils.config  # 读取配置
            cheats = Cheats(config)  # 辅助功能类，处理自瞄、压枪等
            mouse = get_mouse_implementation(config)  # 鼠标操作实现类
            screen = Screen(config)  # 屏幕处理类，识别目标

            print('Unibot ON')  # 启动提示

            # 辅助功能循环，持续检测和响应
            while True:
                delta_time = time.time() - start_time  # 计算上一帧耗时
                start_time = time.time()  # 更新开始时间
                
                reload_config = utils.check_key_binds()  # 检查是否有热键触发配置重载
                if reload_config:
                    break  # 跳出循环，重新加载配置

                # 判断是否需要自瞄或自动射击，或处于调试模式
                if (utils.get_aim_state() or utils.get_trigger_state()) or (config.debug and config.debug_always_on):
                    # 获取目标位置和是否需要触发射击
                    target, trigger = screen.get_target(cheats.recoil_offset)

                    # 如果需要自动射击且目标在准心中心，则点击鼠标
                    if utils.get_trigger_state() and trigger:
                        if config.trigger_delay != 0:
                            # 计算点击延迟，加入随机性
                            delay_before_click = (np.random.randint(config.trigger_randomization) + config.trigger_delay) / 1000
                        else:
                            delay_before_click = 0
                        mouse.click(delay_before_click)  # 执行点击

                    # 根据目标位置计算自瞄移动
                    cheats.calculate_aim(utils.get_aim_state(), target)

                # 如果开启连发模式，则持续点击鼠标
                if utils.get_rapid_fire_state():
                    mouse.click()

                # 应用压枪（控制鼠标下移以抵消后坐力）
                cheats.apply_recoil(utils.recoil_state, delta_time)

                # 根据计算结果移动鼠标
                if cheats.move_x != 0 or cheats.move_y != 0:
                    mouse.move(cheats.move_x, cheats.move_y)

                # 重置移动值，防止无目标时鼠标漂移
                cheats.move_x, cheats.move_y = (0, 0)

                # 控制循环刷新率，不超过设定的最小循环时间
                time_spent = (time.time() - start_time) * 1000
                if time_spent < config.min_loop_time:
                    time.sleep((config.min_loop_time - time_spent) / 1000)

            # 清理对象，释放资源
            del utils
            del cheats
            del mouse
            del screen
            print('Reloading')  # 配置重载提示
        
    def print_license(self):
        # 打印版权和许可证信息
        print('Unibot  Copyright (C) 2025  vike256 \n'
              'This program comes with ABSOLUTELY NO WARRANTY. \n'
              'This is free software, and you are welcome to redistribute it under certain conditions. \n'
              'For details see <LICENSE.txt>.')
