"""
    Interception Input Handler - Low-level input simulation
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import numpy as np
try:
    import interception
except ImportError:
    print("Warning: Interception library not available")
    interception = None

from .base_input import BaseInputHandler
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from behavior_randomizer import BehaviorRandomizer


class InterceptionInputHandler(BaseInputHandler):
    """Interception driver based input handler for low-level input simulation."""
    
    def __init__(self, config):
        """Initialize the Interception input handler."""
        super().__init__(config)
        self.behavior_randomizer = BehaviorRandomizer()
        
        if interception:
            try:
                interception.auto_capture_devices(mouse=True)
            except Exception as e:
                print(f"Failed to initialize Interception driver: {e}")
                
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement using Interception driver."""
        if not interception:
            print("Interception driver not available")
            return
            
        try:
            # Add human-like movement variations
            adjusted_x, adjusted_y = self.behavior_randomizer.add_movement_jitter(x, y)
            
            # Apply movement
            interception.move_relative(int(adjusted_x), int(adjusted_y))
            
            if self.config.debug:
                print(f'(Interception) Movement: ({int(adjusted_x)}, {int(adjusted_y)})')
                
        except Exception as e:
            print(f"Error in Interception cursor movement: {e}")
            
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input using Interception driver."""
        if not interception:
            print("Interception driver not available")
            return
            
        try:
            # Apply pre-action delay
            if delay_before_action > 0:
                time.sleep(delay_before_action)
                
            self.last_action_time = time.time()
            
            # Get human-like click timing
            press_duration, release_delay = self.behavior_randomizer.get_click_timing()
            
            # Perform action with natural timing
            interception.mouse_down('left')
            time.sleep(press_duration)
            interception.mouse_up('left')
            
            if self.config.debug:
                print(f'(Interception) Action: press={press_duration*1000:.1f}ms')
                
            # Post-action delay
            time.sleep(release_delay)
            
        except Exception as e:
            print(f"Error in Interception action input: {e}")
