"""
    Base Microcontroller Input Handler - Abstract base for microcontroller input
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import abc
import threading
import time
from .base_input import BaseInputHandler


class BaseMicrocontrollerInputHandler(BaseInputHandler, abc.ABC):
    """Abstract base class for microcontroller-based input handlers."""
    
    def __init__(self, config):
        """Initialize the base microcontroller input handler."""
        super().__init__(config)
        self.send_command_lock = threading.Lock()
        self.action_command = 'C\r'
        self.device = None
        
    @staticmethod
    def get_movement_command(x: int, y: int) -> str:
        """Generate movement command string."""
        return f'M{x},{y}\r'
        
    def __del__(self):
        """Cleanup when object is destroyed."""
        self.close_connection()
        
    @abc.abstractmethod
    def connect_to_device(self) -> None:
        """Connect to the microcontroller device."""
        pass
        
    @abc.abstractmethod
    def send_command(self, command: str) -> None:
        """Send command to the microcontroller."""
        pass
        
    @abc.abstractmethod
    def get_response(self) -> str:
        """Get response from the microcontroller."""
        pass
        
    @abc.abstractmethod
    def close_connection(self) -> None:
        """Close connection to the microcontroller."""
        pass
        
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement command."""
        with self.send_command_lock:
            command = self.get_movement_command(x, y)
            self.send_command(command)
            
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input command."""
        if delay_before_action > 0:
            time.sleep(delay_before_action)
            
        self.last_action_time = time.time()
        
        with self.send_command_lock:
            self.send_command(self.action_command)
            
        # Small delay to prevent command flooding
        time.sleep(0.025)
