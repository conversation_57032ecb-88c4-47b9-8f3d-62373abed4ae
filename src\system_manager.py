"""
    System Manager - Configuration and input state management
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import win32api
import time
import sys
from typing import Optional
from settings_manager import SettingsManager


class SystemManager:
    """Manages system configuration and input state monitoring."""
    
    def __init__(self):
        """Initialize the system manager."""
        self.settings_manager = SettingsManager()
        self.config = self.settings_manager
        
        # System state variables
        self.enhancement_active = False
        self.compensation_active = False
        self.auto_action_active = False
        self.rapid_input_active = False
        
        # Key bindings from configuration
        self._load_key_bindings()
        
        # Timing control
        self.key_check_delay = 0.1  # 100ms delay between key checks
        self.last_key_check = {}
        
    def _load_key_bindings(self) -> None:
        """Load key bindings from configuration."""
        try:
            self.key_reload_settings = self.config.key_reload_settings
            self.key_toggle_enhancement = self.config.key_toggle_enhancement
            self.key_toggle_compensation = self.config.key_toggle_compensation
            self.key_system_exit = self.config.key_system_exit
            self.key_auto_action = self.config.key_auto_action
            self.key_rapid_input = self.config.key_rapid_input
            self.enhancement_activation_keys = self.config.enhancement_activation_keys
        except AttributeError as e:
            print(f"Warning: Missing key binding configuration: {e}")
            self._set_default_key_bindings()
            
    def _set_default_key_bindings(self) -> None:
        """Set default key bindings if configuration is missing."""
        self.key_reload_settings = 0x70  # F1
        self.key_toggle_enhancement = 0x71  # F2
        self.key_toggle_compensation = 0x72  # F3
        self.key_system_exit = 0x73  # F4
        self.key_auto_action = 0x06  # Mouse 4
        self.key_rapid_input = 0x05  # Mouse 5
        self.enhancement_activation_keys = [0x01, 0x02]  # Mouse 1 & 2
        
    def check_system_controls(self) -> bool:
        """Check system control keys and handle state changes."""
        current_time = time.time()
        
        # Check reload configuration key
        if self._is_key_pressed_with_delay(self.key_reload_settings, current_time):
            return True  # Signal configuration reload needed
            
        # Check enhancement toggle
        if self._is_key_pressed_with_delay(self.key_toggle_enhancement, current_time):
            self.enhancement_active = not self.enhancement_active
            print(f"Enhancement: {self.enhancement_active}")
            
        # Check compensation toggle
        if self._is_key_pressed_with_delay(self.key_toggle_compensation, current_time):
            self.compensation_active = not self.compensation_active
            print(f"Compensation: {self.compensation_active}")
            
        # Check system exit
        if self._is_key_pressed_with_delay(self.key_system_exit, current_time):
            print("System shutdown initiated")
            sys.exit(0)
            
        return False
        
    def _is_key_pressed_with_delay(self, key_code: int, current_time: float) -> bool:
        """Check if key is pressed with delay to prevent rapid triggering."""
        if win32api.GetAsyncKeyState(key_code) < 0:
            last_press_time = self.last_key_check.get(key_code, 0)
            if current_time - last_press_time > self.key_check_delay:
                self.last_key_check[key_code] = current_time
                return True
        return False
        
    def reload_configuration(self) -> None:
        """Reload system configuration."""
        try:
            self.settings_manager.reload_settings()
            self._load_key_bindings()
            print("Configuration reloaded successfully")
        except Exception as e:
            print(f"Error reloading configuration: {e}")
            
    def get_configuration(self):
        """Get current system configuration."""
        return self.config
        
    def get_enhancement_state(self) -> bool:
        """Get current enhancement state with activation key check."""
        if not self.enhancement_active:
            return False
            
        # Check if enhancement activation keys are pressed
        if self.enhancement_activation_keys[0] == 'off':
            return True
        else:
            for key in self.enhancement_activation_keys:
                if win32api.GetAsyncKeyState(key) < 0:
                    return True
        return False
        
    def get_auto_action_state(self) -> bool:
        """Get current auto action state."""
        return win32api.GetAsyncKeyState(self.key_auto_action) < 0
        
    def get_rapid_input_state(self) -> bool:
        """Get current rapid input state."""
        return win32api.GetAsyncKeyState(self.key_rapid_input) < 0
        
    def get_compensation_state(self) -> bool:
        """Get current compensation state."""
        return self.compensation_active
        
    def set_enhancement_state(self, state: bool) -> None:
        """Set enhancement state."""
        self.enhancement_active = state
        
    def set_compensation_state(self, state: bool) -> None:
        """Set compensation state."""
        self.compensation_active = state
        
    def get_system_status(self) -> dict:
        """Get comprehensive system status."""
        return {
            'enhancement_active': self.enhancement_active,
            'compensation_active': self.compensation_active,
            'auto_action_available': self.get_auto_action_state(),
            'rapid_input_available': self.get_rapid_input_state(),
            'configuration_loaded': self.config is not None
        }
