"""
    Base Input Handler - Abstract base class for input implementations
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import abc
import time
import threading
from typing import Tuple


class BaseInputHandler(abc.ABC):
    """Abstract base class for input handling implementations."""
    
    def __init__(self, config):
        """Initialize the base input handler."""
        self.config = config
        self.action_thread = threading.Thread(target=self.send_action_input)
        self.last_action_time = time.time()
        self.movement_remainder_x = 0.0
        self.movement_remainder_y = 0.0
        
    @abc.abstractmethod
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement command."""
        pass
        
    @abc.abstractmethod
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input command."""
        pass
        
    def calculate_movement_amount(self, move_x: float, move_y: float) -> Tuple[int, int]:
        """Calculate precise movement amount with remainder handling."""
        # Add remainder from previous calculations
        move_x += self.movement_remainder_x
        move_y += self.movement_remainder_y
        
        # Calculate integer movement and new remainder
        int_move_x = int(move_x)
        int_move_y = int(move_y)
        
        self.movement_remainder_x = move_x - int_move_x
        self.movement_remainder_y = move_y - int_move_y
        
        return int_move_x, int_move_y
        
    def perform_action(self, delay_before_action: float = 0) -> None:
        """Perform action with rate limiting."""
        current_time = time.time()
        min_interval = 1.0 / self.config.target_input_rate
        
        if (not self.action_thread.is_alive() and 
            current_time - self.last_action_time >= min_interval):
            
            self.action_thread = threading.Thread(
                target=self.send_action_input, 
                args=(delay_before_action,)
            )
            self.action_thread.start()
            
    def move_cursor(self, x: float, y: float) -> None:
        """Move cursor with precise movement calculation."""
        move_x, move_y = self.calculate_movement_amount(x, y)
        
        if move_x != 0 or move_y != 0:
            self.send_cursor_movement(move_x, move_y)
