"""
    Socket Input Handler - Network-based microcontroller input simulation
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import socket
import time
import numpy as np
from .base_microcontroller_input import BaseMicrocontrollerInputHandler
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from behavior_randomizer import BehaviorRandomizer


class SocketInputHandler(BaseMicrocontrollerInputHandler):
    """Socket communication based input handler for network microcontroller devices."""
    
    def __init__(self, config):
        """Initialize the Socket input handler."""
        super().__init__(config)
        self.behavior_randomizer = BehaviorRandomizer()
        self.socket = None
        self.connect_to_device()
        
    def connect_to_device(self) -> None:
        """Connect to the microcontroller device via socket."""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)  # 5 second timeout
            
            ip = self.config.device_ip
            port = self.config.device_port
            
            self.socket.connect((ip, port))
            print(f"Connected to device at {ip}:{port}")
            
        except Exception as e:
            print(f"Failed to connect to device: {e}")
            self.socket = None
            
    def send_command(self, command: str) -> None:
        """Send command to the microcontroller via socket."""
        if not self.socket:
            return
            
        try:
            self.socket.send(command.encode())
            
            # Wait for acknowledgment
            response = self.socket.recv(1024).decode().strip()
            if not response.startswith("a"):
                print(f"Unexpected device response: {response}")
                
        except Exception as e:
            print(f"Error sending command to device: {e}")
            self._reconnect()
            
    def get_response(self) -> str:
        """Get response from the microcontroller via socket."""
        if not self.socket:
            return ""
            
        try:
            return self.socket.recv(1024).decode().strip()
        except Exception as e:
            print(f"Error reading device response: {e}")
            return ""
            
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement command to microcontroller."""
        if not self.socket:
            return
            
        try:
            # Add human-like movement variations
            adjusted_x, adjusted_y = self.behavior_randomizer.add_movement_jitter(x, y)
            
            # Send movement command
            command = self.get_movement_command(int(adjusted_x), int(adjusted_y))
            self.send_command(command)
            
            if self.config.debug:
                print(f'(Socket) Movement: ({int(adjusted_x)}, {int(adjusted_y)})')
                
        except Exception as e:
            print(f"Error in socket cursor movement: {e}")
            
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input command to microcontroller."""
        if not self.socket:
            return
            
        try:
            # Apply pre-action delay
            if delay_before_action > 0:
                time.sleep(delay_before_action)
                
            self.last_action_time = time.time()
            
            # Send action command
            self.send_command(self.action_command)
            
            if self.config.debug:
                print('(Socket) Action sent')
                
            # Small delay to prevent command flooding
            time.sleep(0.025)
            
        except Exception as e:
            print(f"Error in socket action input: {e}")
            
    def _reconnect(self) -> None:
        """Attempt to reconnect to the device."""
        print("Attempting to reconnect to device...")
        self.close_connection()
        time.sleep(1)
        self.connect_to_device()
        
    def close_connection(self) -> None:
        """Close the socket connection."""
        if self.socket:
            try:
                self.socket.close()
                self.socket = None
                print("Socket connection closed")
            except Exception as e:
                print(f"Error closing socket connection: {e}")
