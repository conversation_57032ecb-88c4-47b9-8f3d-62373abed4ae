"""
    WinAPI Input Handler - Windows API based input simulation
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import threading
import win32api
import win32con
import numpy as np
from .base_input import BaseInputHandler
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from behavior_randomizer import BehaviorRandomizer


class WinApiInputHandler(BaseInputHandler):
    """Windows API based input handler with enhanced human-like behavior."""
    
    def __init__(self, config):
        """Initialize the WinAPI input handler."""
        super().__init__(config)
        self.behavior_randomizer = BehaviorRandomizer()
        
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement using WinAPI."""
        try:
            # Add human-like movement variations
            adjusted_x, adjusted_y = self.behavior_randomizer.add_movement_jitter(x, y)
            
            # Apply movement with slight randomization
            final_x = int(adjusted_x + np.random.normal(0, 0.3))
            final_y = int(adjusted_y + np.random.normal(0, 0.3))
            
            # Send movement command
            win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, final_x, final_y, 0, 0)
            
            if self.config.debug:
                print(f'(WinAPI) Movement: ({final_x}, {final_y})')
                
        except Exception as e:
            print(f"Error in cursor movement: {e}")
            
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input with human-like timing."""
        try:
            # Apply pre-action delay
            if delay_before_action > 0:
                time.sleep(delay_before_action)
                
            self.last_action_time = time.time()
            
            # Get human-like click timing
            press_duration, release_delay = self.behavior_randomizer.get_click_timing()
            
            # Perform action with natural timing
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(press_duration)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            
            if self.config.debug:
                print(f'(WinAPI) Action: press={press_duration*1000:.1f}ms')
                
            # Post-action delay
            time.sleep(release_delay)
            
        except Exception as e:
            print(f"Error in action input: {e}")
            
    def _calculate_movement_steps(self, x: float, y: float) -> list:
        """Calculate movement steps for smooth cursor movement."""
        distance = np.sqrt(x*x + y*y)
        
        if distance < 5:
            return [(int(x), int(y))]
            
        # Calculate number of steps based on distance
        num_steps = max(2, min(8, int(distance / 10)))
        
        # Generate curved movement path
        steps = self.behavior_randomizer.get_movement_curve_points(
            0, 0, x, y, num_steps
        )
        
        # Convert to relative movements
        relative_steps = []
        prev_x, prev_y = 0, 0
        
        for step_x, step_y in steps:
            rel_x = int(step_x - prev_x)
            rel_y = int(step_y - prev_y)
            relative_steps.append((rel_x, rel_y))
            prev_x, prev_y = step_x, step_y
            
        return relative_steps
        
    def move_cursor_smooth(self, x: float, y: float) -> None:
        """Move cursor with smooth, human-like movement."""
        if abs(x) < 0.5 and abs(y) < 0.5:
            return
            
        # Calculate movement steps
        steps = self._calculate_movement_steps(x, y)
        
        # Execute movement steps with timing
        for i, (step_x, step_y) in enumerate(steps):
            if step_x != 0 or step_y != 0:
                self.send_cursor_movement(step_x, step_y)
                
                # Add small delay between steps for natural movement
                if i < len(steps) - 1:
                    delay = np.random.uniform(0.001, 0.003)  # 1-3ms
                    time.sleep(delay)
