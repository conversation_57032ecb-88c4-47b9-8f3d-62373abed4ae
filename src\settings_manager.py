"""
    Settings Manager - Configuration file management
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import os
from configparser import ConfigParser
from typing import List, Optional


class SettingsManager:
    """Manages application settings and configuration."""
    
    def __init__(self):
        """Initialize the settings manager."""
        self.parser = ConfigParser()
        
        # Input/Output settings
        self.input_method = None
        self.device_ip = None
        self.device_port = None
        self.serial_port = None
        
        # Visual processing settings
        self.target_grouping_threshold = None
        self.color_range_upper = None
        self.color_range_lower = None
        self.capture_region_x = None
        self.capture_region_y = None
        self.processing_region_x = None
        self.processing_region_y = None
        self.min_frame_time = None
        self.auto_detect_resolution = None
        self.resolution_x = None
        self.resolution_y = None
        
        # Movement settings
        self.display_center_offset = None
        self.movement_smoothing_factor = None
        self.movement_speed = None
        self.vertical_speed_multiplier = None
        self.target_height_ratio = None
        
        # Compensation settings
        self.compensation_mode = None
        self.compensation_x = None
        self.compensation_y = None
        self.max_compensation_offset = None
        self.compensation_recovery = None
        
        # Action settings
        self.action_delay = None
        self.action_randomization = None
        self.action_threshold = None
        
        # Input rate settings
        self.target_input_rate = None
        
        # Key bindings
        self.key_reload_settings = None
        self.key_toggle_enhancement = None
        self.key_toggle_compensation = None
        self.key_system_exit = None
        self.key_auto_action = None
        self.key_rapid_input = None
        self.enhancement_activation_keys = []
        
        # Debug settings
        self.debug = None
        self.debug_always_on = None
        self.display_mode = None
        
        # Load configuration
        self.settings_path = os.path.join(os.path.dirname(__file__), '../settings.ini')
        self._load_settings()
        
    def _load_settings(self) -> None:
        """Load settings from configuration file."""
        try:
            self.parser.read(self.settings_path)
            self._parse_all_sections()
        except Exception as e:
            print(f"Error loading settings: {e}")
            self._create_default_settings()
            
    def _parse_all_sections(self) -> None:
        """Parse all configuration sections."""
        self._parse_enhancement_settings()
        self._parse_device_settings()
        self._parse_visual_settings()
        self._parse_compensation_settings()
        self._parse_action_settings()
        self._parse_input_rate_settings()
        self._parse_key_bindings()
        self._parse_debug_settings()
        
    def _parse_enhancement_settings(self) -> None:
        """Parse enhancement-related settings."""
        try:
            value = self.parser.get('enhancement', 'input_method').lower()
            valid_methods = ['winapi', 'interception_driver', 'microcontroller_serial', 'microcontroller_socket']
            if value in valid_methods:
                self.input_method = value
            else:
                print('Warning: Invalid input_method value')
                
            self.display_center_offset = int(self.parser.get('enhancement', 'display_center_offset'))
            
            value = float(self.parser.get('enhancement', 'movement_smoothing_factor'))
            if 0 <= value <= 1:
                self.movement_smoothing_factor = 1 - value / 1.25
            else:
                print('Warning: Invalid movement_smoothing_factor value')
                
            self.movement_speed = float(self.parser.get('enhancement', 'movement_speed'))
            self.vertical_speed_multiplier = float(self.parser.get('enhancement', 'vertical_speed_multiplier'))
            
            value = float(self.parser.get('enhancement', 'target_height_ratio'))
            if 0 <= value <= 1:
                self.target_height_ratio = value
            else:
                print('Warning: Invalid target_height_ratio value')
                
        except Exception as e:
            print(f"Error parsing enhancement settings: {e}")
            
    def _parse_device_settings(self) -> None:
        """Parse device communication settings."""
        try:
            if self.input_method == 'microcontroller_socket':
                self.device_ip = self.parser.get('device', 'device_ip')
                self.device_port = int(self.parser.get('device', 'device_port'))
            elif self.input_method == 'microcontroller_serial':
                self.serial_port = self.parser.get('device', 'serial_port')
        except Exception as e:
            print(f"Error parsing device settings: {e}")
            
    def _parse_visual_settings(self) -> None:
        """Parse visual processing settings."""
        try:
            # Parse grouping threshold
            threshold_str = self.parser.get('visual', 'target_grouping_threshold')
            self.target_grouping_threshold = [int(x.strip()) for x in threshold_str.split(',')]
            
            # Parse color ranges
            upper_str = self.parser.get('visual', 'color_range_upper')
            self.color_range_upper = tuple(int(x.strip()) for x in upper_str.split(','))
            
            lower_str = self.parser.get('visual', 'color_range_lower')
            self.color_range_lower = tuple(int(x.strip()) for x in lower_str.split(','))
            
            # Parse region settings
            self.capture_region_x = int(self.parser.get('visual', 'capture_region_x'))
            self.capture_region_y = int(self.parser.get('visual', 'capture_region_y'))
            self.processing_region_x = int(self.parser.get('visual', 'processing_region_x'))
            self.processing_region_y = int(self.parser.get('visual', 'processing_region_y'))
            
            # Parse timing settings
            max_fps = int(self.parser.get('visual', 'max_frames_per_sec'))
            self.min_frame_time = 1000 / max_fps if max_fps > 0 else 16.67  # Default 60 FPS
            
            # Parse resolution settings
            self.auto_detect_resolution = self.parser.get('visual', 'auto_detect_resolution').lower() == 'true'
            self.resolution_x = int(self.parser.get('visual', 'resolution_x'))
            self.resolution_y = int(self.parser.get('visual', 'resolution_y'))
            
        except Exception as e:
            print(f"Error parsing visual settings: {e}")
            
    def _parse_compensation_settings(self) -> None:
        """Parse compensation settings."""
        try:
            value = self.parser.get('compensation', 'mode').lower()
            valid_modes = ['move', 'offset']
            if value in valid_modes:
                self.compensation_mode = value
            else:
                print('Warning: Invalid compensation mode')
                
            self.compensation_x = float(self.parser.get('compensation', 'compensation_x'))
            self.compensation_y = float(self.parser.get('compensation', 'compensation_y'))
            self.max_compensation_offset = float(self.parser.get('compensation', 'max_offset'))
            self.compensation_recovery = float(self.parser.get('compensation', 'recovery_rate'))
            
        except Exception as e:
            print(f"Error parsing compensation settings: {e}")
            
    def _parse_action_settings(self) -> None:
        """Parse action timing settings."""
        try:
            self.action_delay = int(self.parser.get('action', 'action_delay'))
            self.action_randomization = int(self.parser.get('action', 'action_randomization'))
            self.action_threshold = int(self.parser.get('action', 'action_threshold'))
        except Exception as e:
            print(f"Error parsing action settings: {e}")
            
    def _parse_input_rate_settings(self) -> None:
        """Parse input rate settings."""
        try:
            self.target_input_rate = float(self.parser.get('input_rate', 'target_rate'))
        except Exception as e:
            print(f"Error parsing input rate settings: {e}")
            
    def _parse_key_bindings(self) -> None:
        """Parse key binding settings."""
        try:
            self.key_reload_settings = self._parse_hex_key(self.parser.get('keys', 'key_reload_settings'))
            self.key_toggle_enhancement = self._parse_hex_key(self.parser.get('keys', 'key_toggle_enhancement'))
            self.key_toggle_compensation = self._parse_hex_key(self.parser.get('keys', 'key_toggle_compensation'))
            self.key_system_exit = self._parse_hex_key(self.parser.get('keys', 'key_system_exit'))
            self.key_auto_action = self._parse_hex_key(self.parser.get('keys', 'key_auto_action'))
            self.key_rapid_input = self._parse_hex_key(self.parser.get('keys', 'key_rapid_input'))
            
            # Parse enhancement activation keys
            activation_keys_str = self.parser.get('keys', 'enhancement_activation_keys')
            if activation_keys_str.lower() != 'off':
                key_list = activation_keys_str.split(',')
                self.enhancement_activation_keys = [self._parse_hex_key(key.strip()) for key in key_list]
            else:
                self.enhancement_activation_keys = ['off']
                
        except Exception as e:
            print(f"Error parsing key bindings: {e}")
            
    def _parse_debug_settings(self) -> None:
        """Parse debug settings."""
        try:
            self.debug = self.parser.get('debug', 'enabled').lower() == 'true'
            self.debug_always_on = self.parser.get('debug', 'always_on').lower() == 'true'
            
            value = self.parser.get('debug', 'display_mode').lower()
            valid_modes = ['game', 'mask']
            if value in valid_modes:
                self.display_mode = value
            else:
                print('Warning: Invalid display_mode value')
                
        except Exception as e:
            print(f"Error parsing debug settings: {e}")
            
    @staticmethod
    def _parse_hex_key(key_string: str) -> int:
        """Parse hexadecimal key code."""
        return int(key_string, 16)
        
    def _create_default_settings(self) -> None:
        """Create default settings if configuration file is missing."""
        print("Creating default settings...")
        # Set reasonable defaults
        self.input_method = 'winapi'
        self.movement_smoothing_factor = 0.8
        self.movement_speed = 1.0
        self.vertical_speed_multiplier = 1.0
        # ... (other defaults)
        
    def reload_settings(self) -> None:
        """Reload settings from file."""
        self._load_settings()
        
    def save_settings(self) -> None:
        """Save current settings to file."""
        # Implementation for saving settings back to file
        pass
