# DisplayHelper 配置手册

## 📋 配置文件完整参考

### 配置文件结构概览

```ini
[enhancement]      # 核心功能配置
[device]          # 硬件设备配置  
[visual]          # 视觉处理配置
[compensation]    # 移动补偿配置
[action]          # 动作行为配置
[input_rate]      # 输入频率配置
[keys]            # 按键绑定配置
[debug]           # 调试模式配置
```

---

## ⚙️ 详细参数说明

### [enhancement] 核心功能配置

#### input_method（输入方法）
```ini
input_method = winapi
```

**可选值：**
- `winapi`：Windows API 方法
  - 优点：兼容性最好，无需额外驱动
  - 缺点：可能被某些安全软件检测
  - 适用：一般用户，兼容性要求高

- `interception_driver`：Interception 驱动方法
  - 优点：更底层的输入模拟，检测难度更高
  - 缺点：需要安装第三方驱动，需要管理员权限
  - 适用：高级用户，安全要求高

- `microcontroller_serial`：串行微控制器方法
  - 优点：硬件级输入，几乎无法检测
  - 缺点：需要额外硬件，设置复杂
  - 适用：专业用户，最高安全要求

- `microcontroller_socket`：网络微控制器方法
  - 优点：硬件级输入，支持远程控制
  - 缺点：需要网络配置，延迟可能较高
  - 适用：分布式部署，远程控制

#### movement_smoothing_factor（移动平滑因子）
```ini
movement_smoothing_factor = 0.3
```

**数值范围：** 0.0 - 1.0

**效果说明：**
- `0.0`：无平滑，最快响应，可能出现抖动
- `0.1-0.3`：轻度平滑，快速响应，推荐竞技使用
- `0.4-0.6`：中度平滑，平衡响应和稳定性
- `0.7-1.0`：重度平滑，最稳定，但响应较慢

**调优建议：**
```ini
# 快速响应场景
movement_smoothing_factor = 0.1

# 平衡场景  
movement_smoothing_factor = 0.3

# 稳定性优先场景
movement_smoothing_factor = 0.6
```

#### movement_speed（移动速度）
```ini
movement_speed = 1.0
```

**数值范围：** 0.1 - 5.0

**效果说明：**
- `< 1.0`：减慢移动速度，提高精确度
- `= 1.0`：标准移动速度
- `> 1.0`：加快移动速度，提高响应性

**不同场景推荐：**
```ini
# 精确模式
movement_speed = 0.8

# 标准模式
movement_speed = 1.0

# 快速模式  
movement_speed = 1.3

# 极速模式（不推荐超过2.0）
movement_speed = 1.8
```

#### vertical_speed_multiplier（垂直速度倍数）
```ini
vertical_speed_multiplier = 1.0
```

**用途：** 独立调整垂直方向的移动速度

**常用配置：**
```ini
# 垂直移动较慢（适合宽屏显示器）
vertical_speed_multiplier = 0.8

# 垂直移动较快（适合高分辨率显示器）
vertical_speed_multiplier = 1.2
```

#### target_height_ratio（目标高度比例）
```ini
target_height_ratio = 0.5
```

**数值范围：** 0.0 - 1.0

**效果说明：**
- `0.0`：瞄准目标顶部
- `0.3`：瞄准目标上部（头部区域）
- `0.5`：瞄准目标中心
- `0.7`：瞄准目标下部（身体区域）
- `1.0`：瞄准目标底部

### [visual] 视觉处理配置

#### 颜色检测配置

```ini
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150
```

**HSV 颜色空间说明：**
- **H（色调）**：0-179，决定颜色类型
- **S（饱和度）**：0-255，决定颜色纯度
- **V（亮度）**：0-255，决定颜色明暗

**常用颜色配置：**

```ini
# 红色系目标
color_range_upper = 10, 255, 255
color_range_lower = 0, 120, 70

# 橙色系目标
color_range_upper = 25, 255, 255
color_range_lower = 10, 120, 70

# 黄色系目标
color_range_upper = 35, 255, 255
color_range_lower = 20, 120, 70

# 绿色系目标
color_range_upper = 80, 255, 255
color_range_lower = 40, 120, 70

# 青色系目标
color_range_upper = 100, 255, 255
color_range_lower = 80, 120, 70

# 蓝色系目标
color_range_upper = 130, 255, 255
color_range_lower = 100, 120, 70

# 紫色系目标
color_range_upper = 160, 255, 255
color_range_lower = 130, 120, 70

# 粉色系目标
color_range_upper = 179, 255, 255
color_range_lower = 160, 120, 70
```

#### 捕获区域配置

```ini
capture_region_x = 256
capture_region_y = 256
processing_region_x = 256  
processing_region_y = 256
```

**区域大小建议：**

```ini
# 小区域（性能优先）
capture_region_x = 128
capture_region_y = 128

# 中等区域（平衡）
capture_region_x = 256
capture_region_y = 256

# 大区域（覆盖范围优先）
capture_region_x = 384
capture_region_y = 384

# 超大区域（最大覆盖，高性能设备）
capture_region_x = 512
capture_region_y = 512
```

**性能影响：**
- 区域面积与处理时间成正比
- 256x256 区域约需 5-10ms 处理时间
- 512x512 区域约需 15-25ms 处理时间

#### 帧率配置

```ini
max_frames_per_sec = 60
```

**不同设备推荐：**

```ini
# 低性能设备
max_frames_per_sec = 30

# 中等性能设备
max_frames_per_sec = 60

# 高性能设备
max_frames_per_sec = 120

# 极高性能设备
max_frames_per_sec = 240
```

### [compensation] 补偿配置

#### 补偿模式

```ini
mode = move
```

**可选模式：**

- `move`：直接移动补偿
  - 原理：直接在计算的移动量上添加补偿
  - 适用：需要持续补偿的场景
  - 特点：响应快，效果直接

- `offset`：偏移补偿  
  - 原理：通过累积偏移量进行补偿
  - 适用：需要渐进补偿的场景
  - 特点：更自然，但响应较慢

#### 补偿参数

```ini
compensation_x = 0.0
compensation_y = 15.0
max_offset = 100
recovery_rate = 50.0
```

**参数说明：**
- `compensation_x/y`：每秒的补偿量（像素）
- `max_offset`：最大累积偏移量
- `recovery_rate`：偏移恢复速率

**典型配置：**

```ini
# 轻度补偿
compensation_y = 8.0
max_offset = 50
recovery_rate = 30.0

# 中度补偿
compensation_y = 15.0
max_offset = 100  
recovery_rate = 50.0

# 重度补偿
compensation_y = 25.0
max_offset = 150
recovery_rate = 75.0
```

### [action] 动作配置

#### 延迟配置

```ini
action_delay = 25
action_randomization = 30
```

**人性化延迟建议：**

```ini
# 快速响应（竞技向）
action_delay = 15
action_randomization = 20

# 标准响应（平衡）
action_delay = 25
action_randomization = 30

# 保守响应（安全向）
action_delay = 50
action_randomization = 40

# 极保守响应
action_delay = 100
action_randomization = 60
```

### [keys] 按键配置

#### 虚拟键码对照表

```ini
# 功能键
F1 = 0x70    F2 = 0x71    F3 = 0x72    F4 = 0x73
F5 = 0x74    F6 = 0x75    F7 = 0x76    F8 = 0x77
F9 = 0x78    F10 = 0x79   F11 = 0x7A   F12 = 0x7B

# 鼠标按键
鼠标左键 = 0x01    鼠标右键 = 0x02    鼠标中键 = 0x04
鼠标侧键4 = 0x05   鼠标侧键5 = 0x06

# 数字键
0 = 0x30    1 = 0x31    2 = 0x32    3 = 0x33    4 = 0x34
5 = 0x35    6 = 0x36    7 = 0x37    8 = 0x38    9 = 0x39

# 字母键
A = 0x41    B = 0x42    C = 0x43    D = 0x44    E = 0x45
F = 0x46    G = 0x47    H = 0x48    I = 0x49    J = 0x4A
K = 0x4B    L = 0x4C    M = 0x4D    N = 0x4E    O = 0x4F
P = 0x50    Q = 0x51    R = 0x52    S = 0x53    T = 0x54
U = 0x55    V = 0x56    W = 0x57    X = 0x58    Y = 0x59
Z = 0x5A

# 特殊键
空格 = 0x20      回车 = 0x0D      Tab = 0x09
Shift = 0x10     Ctrl = 0x11      Alt = 0x12
Esc = 0x1B       删除 = 0x2E      退格 = 0x08

# 方向键
上 = 0x26        下 = 0x28        左 = 0x25        右 = 0x27
```

#### 推荐按键配置

```ini
# 标准配置（推荐）
key_reload_settings = 0x70        # F1
key_toggle_enhancement = 0x71     # F2  
key_toggle_compensation = 0x72    # F3
key_system_exit = 0x73           # F4
key_auto_action = 0x06           # 鼠标侧键4
key_rapid_input = 0x05           # 鼠标侧键5
enhancement_activation_keys = 0x01, 0x02  # 鼠标左右键

# 键盘优先配置
key_auto_action = 0x51           # Q键
key_rapid_input = 0x45           # E键
enhancement_activation_keys = 0x20  # 空格键

# 单手操作配置
enhancement_activation_keys = off  # 无需激活键
```

---

## 🎯 场景化配置方案

### 竞技场景配置

```ini
[enhancement]
input_method = interception_driver
movement_smoothing_factor = 0.1
movement_speed = 1.2
vertical_speed_multiplier = 1.0
target_height_ratio = 0.3

[visual]
capture_region_x = 384
capture_region_y = 384
max_frames_per_sec = 120

[compensation]
mode = move
compensation_y = 12.0
recovery_rate = 75.0

[action]
action_delay = 15
action_randomization = 20

[debug]
enabled = false
```

### 休闲场景配置

```ini
[enhancement]
input_method = winapi
movement_smoothing_factor = 0.4
movement_speed = 0.9
target_height_ratio = 0.5

[visual]
capture_region_x = 256
capture_region_y = 256
max_frames_per_sec = 60

[action]
action_delay = 40
action_randomization = 35

[debug]
enabled = false
```

### 测试场景配置

```ini
[enhancement]
movement_smoothing_factor = 0.6
movement_speed = 0.7

[visual]
capture_region_x = 256
capture_region_y = 256
max_frames_per_sec = 30

[action]
action_delay = 100
action_randomization = 50

[debug]
enabled = true
always_on = true
display_mode = mask
```

---

## 📊 性能调优矩阵

### CPU 使用率优化

| CPU使用率 | 推荐配置 | 说明 |
|-----------|----------|------|
| < 30% | 高性能配置 | 可以使用最大区域和帧率 |
| 30-60% | 标准配置 | 平衡性能和质量 |
| 60-80% | 优化配置 | 需要降低帧率或区域 |
| > 80% | 最小配置 | 大幅降低处理负载 |

### 内存使用优化

| 可用内存 | 推荐配置 | 说明 |
|----------|----------|------|
| > 4GB | 无限制 | 可以使用任何配置 |
| 2-4GB | 中等限制 | 避免过大的捕获区域 |
| < 2GB | 严格限制 | 使用最小配置 |

### 网络延迟优化（微控制器模式）

| 网络延迟 | 推荐配置 | 说明 |
|----------|----------|------|
| < 5ms | 无限制 | 局域网有线连接 |
| 5-20ms | 轻微调整 | 局域网WiFi连接 |
| > 20ms | 不推荐 | 网络质量不适合实时控制 |

---

## 🔧 故障诊断配置

### 调试配置模板

```ini
# 完整调试配置
[debug]
enabled = true
always_on = true
display_mode = mask

[visual]
capture_region_x = 256
capture_region_y = 256
max_frames_per_sec = 30

[enhancement]
movement_smoothing_factor = 0.5
movement_speed = 0.5
```

### 最小化配置模板

```ini
# 问题排查用最小配置
[visual]
capture_region_x = 64
capture_region_y = 64
max_frames_per_sec = 15

[enhancement]
movement_smoothing_factor = 0.8
movement_speed = 0.3

[action]
action_delay = 200
action_randomization = 100
```

---

**配置建议：** 建议从保守配置开始，根据实际效果和性能表现逐步调整参数，避免一次性使用激进配置。
