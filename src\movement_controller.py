"""
    Movement Controller - Advanced cursor movement and compensation system
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import math
import win32api
import numpy as np
from typing import Optional, Tuple
from behavior_randomizer import BehaviorRandomizer


class MovementController:
    """Advanced cursor movement and compensation controller."""
    
    def __init__(self, config):
        """Initialize the movement controller."""
        self.config = config
        self.movement_x = 0.0
        self.movement_y = 0.0
        self.previous_x = 0.0
        self.previous_y = 0.0
        self.compensation_offset = 0.0
        
        # Enhanced behavior simulation
        self.behavior_randomizer = BehaviorRandomizer()
        
        # Movement history for advanced smoothing
        self.movement_history = []
        self.max_history_length = 5
        
        # Adaptive parameters
        self.adaptive_smoothing = config.movement_smoothing_factor
        self.last_target_time = time.time()
        
    def calculate_movement(self, enhancement_active: bool, target_info: Optional[Tuple]) -> None:
        """Calculate cursor movement based on target information."""
        if not enhancement_active or target_info is None:
            self._reset_movement_state()
            return
            
        target_x, target_y = target_info[:2]
        current_time = time.time()
        
        # Apply movement scaling
        scaled_x = target_x * self.config.movement_speed
        scaled_y = target_y * self.config.movement_speed * self.config.vertical_speed_multiplier
        
        # Apply adaptive smoothing
        smoothing_factor = self._get_adaptive_smoothing(current_time)
        
        # Calculate smoothed movement
        smoothed_x = (1 - smoothing_factor) * self.previous_x + smoothing_factor * scaled_x
        smoothed_y = (1 - smoothing_factor) * self.previous_y + smoothing_factor * scaled_y
        
        # Add human-like behavior variations
        final_x, final_y = self.behavior_randomizer.add_movement_jitter(smoothed_x, smoothed_y)
        
        # Apply movement prediction for better tracking
        final_x, final_y = self._apply_movement_prediction(final_x, final_y)
        
        # Store calculated values
        self.previous_x, self.previous_y = smoothed_x, smoothed_y
        self.movement_x, self.movement_y = final_x, final_y
        
        # Update movement history
        self._update_movement_history(final_x, final_y)
        self.last_target_time = current_time
        
    def apply_compensation(self, compensation_active: bool, delta_time: float) -> None:
        """Apply movement compensation based on configuration."""
        if not compensation_active or delta_time <= 0:
            return
            
        config = self.config
        
        # Mode: direct movement compensation
        if config.compensation_mode == 'move' and win32api.GetAsyncKeyState(0x01) < 0:
            # Add human-like variation to compensation
            comp_x = config.compensation_x * delta_time
            comp_y = config.compensation_y * delta_time
            
            # Add slight randomization to avoid detection
            comp_x += np.random.normal(0, abs(comp_x) * 0.05)
            comp_y += np.random.normal(0, abs(comp_y) * 0.05)
            
            self.movement_x += comp_x
            self.movement_y += comp_y
            
        # Mode: offset compensation
        elif config.compensation_mode == 'offset':
            if win32api.GetAsyncKeyState(0x01) < 0:
                # Increase offset while active
                if self.compensation_offset < config.max_compensation_offset:
                    offset_increase = config.compensation_y * delta_time
                    # Add slight variation
                    offset_increase += np.random.normal(0, offset_increase * 0.03)
                    
                    self.compensation_offset += offset_increase
                    if self.compensation_offset > config.max_compensation_offset:
                        self.compensation_offset = config.max_compensation_offset
            else:
                # Gradually reset offset when not active
                if self.compensation_offset > 0:
                    recovery_rate = config.compensation_recovery * delta_time
                    self.compensation_offset = max(0, self.compensation_offset - recovery_rate)
                    
    def get_movement_delta(self) -> Tuple[float, float]:
        """Get the calculated movement delta."""
        return self.movement_x, self.movement_y
        
    def reset_movement_delta(self) -> None:
        """Reset movement delta values."""
        self.movement_x = 0.0
        self.movement_y = 0.0
        
    def get_offset_compensation(self) -> float:
        """Get current offset compensation value."""
        return self.compensation_offset
        
    def _reset_movement_state(self) -> None:
        """Reset movement state when no target is present."""
        # Gradually reduce previous values to zero for natural deceleration
        decay_factor = 0.8
        self.previous_x *= decay_factor
        self.previous_y *= decay_factor
        
        # Clear movement if values are very small
        if abs(self.previous_x) < 0.1:
            self.previous_x = 0.0
        if abs(self.previous_y) < 0.1:
            self.previous_y = 0.0
            
    def _get_adaptive_smoothing(self, current_time: float) -> float:
        """Calculate adaptive smoothing factor based on movement patterns."""
        base_smoothing = self.config.movement_smoothing_factor
        
        # Adjust smoothing based on time since last target
        time_since_target = current_time - self.last_target_time
        
        if time_since_target > 0.5:  # No target for 500ms
            # Increase smoothing for more stable movement
            adaptive_factor = min(1.5, 1.0 + time_since_target * 0.2)
        else:
            # Normal smoothing for active tracking
            adaptive_factor = 1.0
            
        # Apply behavioral variation
        final_smoothing = self.behavior_randomizer.get_smoothing_factor(
            base_smoothing * adaptive_factor
        )
        
        return max(0.0, min(1.0, final_smoothing))
        
    def _apply_movement_prediction(self, x: float, y: float) -> Tuple[float, float]:
        """Apply movement prediction for smoother tracking."""
        if len(self.movement_history) < 2:
            return x, y
            
        # Calculate movement velocity from history
        recent_movements = self.movement_history[-2:]
        velocity_x = recent_movements[-1][0] - recent_movements[-2][0]
        velocity_y = recent_movements[-1][1] - recent_movements[-2][1]
        
        # Apply small prediction component (10% of velocity)
        prediction_factor = 0.1
        predicted_x = x + velocity_x * prediction_factor
        predicted_y = y + velocity_y * prediction_factor
        
        return predicted_x, predicted_y
        
    def _update_movement_history(self, x: float, y: float) -> None:
        """Update movement history for analysis."""
        self.movement_history.append((x, y))
        
        # Maintain history length
        if len(self.movement_history) > self.max_history_length:
            self.movement_history.pop(0)
            
    def get_movement_statistics(self) -> dict:
        """Get movement statistics for analysis."""
        if not self.movement_history:
            return {}
            
        movements = np.array(self.movement_history)
        
        return {
            'average_movement': np.mean(movements, axis=0).tolist(),
            'movement_variance': np.var(movements, axis=0).tolist(),
            'total_movements': len(self.movement_history),
            'current_offset': self.compensation_offset
        }
