"""
    Unibot, an open-source colorbot.
    Copyright (C) 2025 vike256

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import win32api
from time import sleep

from configReader import ConfigReader


class Utils:
    """工具类，用于处理键盘输入检测和游戏功能状态管理"""
    
    def __init__(self):
        """初始化工具类实例，加载配置并设置初始状态"""
        self.config = ConfigReader()
        self.reload_config()

        self.delay = 0.25  # 按键检测的延迟时间，防止状态切换过快
        # 从配置文件中读取各种功能的按键码
        self.key_reload_config = self.config.key_reload_config
        self.key_toggle_aim = self.config.key_toggle_aim
        self.key_toggle_recoil = self.config.key_toggle_recoil
        self.key_exit = self.config.key_exit
        self.key_trigger = self.config.key_trigger
        self.key_rapid_fire = self.config.key_rapid_fire
        self.aim_keys = self.config.aim_keys
        self.aim_state = False  # 瞄准功能的开关状态
        self.recoil_state = False  # 后坐力补偿功能的开关状态

    def check_key_binds(self):
        """检查按键状态并执行相应操作
        
        Returns:
            bool: 如果需要重新加载配置文件则返回True，否则返回False
        """
        # 检查是否按下重新加载配置的按键
        if win32api.GetAsyncKeyState(self.key_reload_config) < 0:
            return True

        # 检查是否按下切换瞄准功能的按键
        if win32api.GetAsyncKeyState(self.key_toggle_aim) < 0:
            self.aim_state = not self.aim_state  # 切换瞄准功能状态
            print("AIM: " + str(self.aim_state))  # 打印当前瞄准功能状态
            sleep(self.delay)  # 延迟防止重复触发

        # 检查是否按下切换后坐力补偿功能的按键
        if win32api.GetAsyncKeyState(self.key_toggle_recoil) < 0:
            self.recoil_state = not self.recoil_state  # 切换后坐力补偿功能状态
            print("RECOIL: " + str(self.recoil_state))  # 打印当前后坐力补偿功能状态
            sleep(self.delay)  # 延迟防止重复触发
        
        # 检查是否按下退出程序的按键
        if win32api.GetAsyncKeyState(self.key_exit) < 0:
            print("Exiting")  # 打印退出信息
            exit(1)  # 退出程序
        return False

    def reload_config(self):
        """重新加载配置文件"""
        self.config.read_config()

    def get_aim_state(self):
        """获取瞄准功能的当前状态
        
        Returns:
            bool: 如果瞄准功能启用且满足条件则返回True，否则返回False
        """
        # 只有在瞄准功能开启的情况下才继续检查
        if self.aim_state:
            # 如果aim_keys的第一个元素是'off'，表示不需要额外按键即可启用
            if self.aim_keys[0] == 'off':
                return True
            else:
                # 遍历所有瞄准辅助按键，检查是否有任何一个被按下
                for key in self.aim_keys:
                    if win32api.GetAsyncKeyState(key) < 0:
                        return True
        return False

    def get_trigger_state(self):
        """获取触发功能（自动射击）的当前状态
        
        Returns:
            bool: 如果触发键被按下则返回True，否则返回False
        """
        if win32api.GetAsyncKeyState(self.key_trigger) < 0:
            return True
        return False

    def get_rapid_fire_state(self):
        """获取快速射击功能的当前状态
        
        Returns:
            bool: 如果快速射击键被按下则返回True，否则返回False
        """
        if win32api.GetAsyncKeyState(self.key_rapid_fire) < 0:
            return True
        return False

    @staticmethod
    def print_attributes(obj):
        """打印对象的所有属性和值（静态方法）
        
        Args:
            obj: 需要打印属性的任意对象
        """
        attributes = vars(obj)
        for attribute, value in attributes.items():
            print(f'{attribute}: {value}')