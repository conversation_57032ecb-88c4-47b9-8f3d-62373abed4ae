"""Error related constants for win32

Generated by h2py from winerror.h
"""

# Few extras added manually...
TRUST_E_PROVIDER_UNKNOWN = -**********
TRUST_E_ACTION_UNKNOWN = -**********
TRUST_E_SUBJECT_FORM_UNKNOWN = -**********
TRUST_E_SUBJECT_NOT_TRUSTED = -**********
# up to here...

FACILITY_WINRM = 51
FACILITY_WINDOWSUPDATE = 36
FACILITY_WINDOWS_DEFENDER = 80
FACILITY_WINDOWS_CE = 24
FACILITY_WINDOWS = 8
FACILITY_URT = 19
FACILITY_UMI = 22
FACILITY_TPM_SOFTWARE = 41
FACILITY_TPM_SERVICES = 40
FACILITY_SXS = 23
FACILITY_STORAGE = 3
FACILITY_STATE_MANAGEMENT = 34
FACILITY_SSPI = 9
FACILITY_SCARD = 16
FACILITY_SHELL = 39
FACILITY_SETUPAPI = 15
FACILITY_SECURITY = 9
FACILITY_RPC = 1
FACILITY_PLA = 48
FACILITY_WIN32 = 7
FACILITY_CONTROL = 10
FACILITY_NULL = 0
FACILITY_NDIS = 52
FACILITY_METADIRECTORY = 35
FACILITY_MSMQ = 14
FACILITY_MEDIASERVER = 13
FACILITY_INTERNET = 12
FACILITY_ITF = 4
FACILITY_USERMODE_HYPERVISOR = 53
FACILITY_HTTP = 25
FACILITY_GRAPHICS = 38
FACILITY_FWP = 50
FACILITY_FVE = 49
FACILITY_USERMODE_FILTER_MANAGER = 31
FACILITY_DPLAY = 21
FACILITY_DISPATCH = 2
FACILITY_DIRECTORYSERVICE = 37
FACILITY_CONFIGURATION = 33
FACILITY_COMPLUS = 17
FACILITY_USERMODE_COMMONLOG = 26
FACILITY_CMI = 54
FACILITY_CERT = 11
FACILITY_BACKGROUNDCOPY = 32
FACILITY_ACS = 20
FACILITY_AAF = 18
ERROR_SUCCESS = 0
NO_ERROR = 0
S_OK = 0
S_FALSE = 1
ERROR_INVALID_FUNCTION = 1
ERROR_FILE_NOT_FOUND = 2
ERROR_PATH_NOT_FOUND = 3
ERROR_TOO_MANY_OPEN_FILES = 4
ERROR_ACCESS_DENIED = 5
ERROR_INVALID_HANDLE = 6
ERROR_ARENA_TRASHED = 7
ERROR_NOT_ENOUGH_MEMORY = 8
ERROR_INVALID_BLOCK = 9
ERROR_BAD_ENVIRONMENT = 10
ERROR_BAD_FORMAT = 11
ERROR_INVALID_ACCESS = 12
ERROR_INVALID_DATA = 13
ERROR_OUTOFMEMORY = 14
ERROR_INVALID_DRIVE = 15
ERROR_CURRENT_DIRECTORY = 16
ERROR_NOT_SAME_DEVICE = 17
ERROR_NO_MORE_FILES = 18
ERROR_WRITE_PROTECT = 19
ERROR_BAD_UNIT = 20
ERROR_NOT_READY = 21
ERROR_BAD_COMMAND = 22
ERROR_CRC = 23
ERROR_BAD_LENGTH = 24
ERROR_SEEK = 25
ERROR_NOT_DOS_DISK = 26
ERROR_SECTOR_NOT_FOUND = 27
ERROR_OUT_OF_PAPER = 28
ERROR_WRITE_FAULT = 29
ERROR_READ_FAULT = 30
ERROR_GEN_FAILURE = 31
ERROR_SHARING_VIOLATION = 32
ERROR_LOCK_VIOLATION = 33
ERROR_WRONG_DISK = 34
ERROR_SHARING_BUFFER_EXCEEDED = 36
ERROR_HANDLE_EOF = 38
ERROR_HANDLE_DISK_FULL = 39
ERROR_NOT_SUPPORTED = 50
ERROR_REM_NOT_LIST = 51
ERROR_DUP_NAME = 52
ERROR_BAD_NETPATH = 53
ERROR_NETWORK_BUSY = 54
ERROR_DEV_NOT_EXIST = 55
ERROR_TOO_MANY_CMDS = 56
ERROR_ADAP_HDW_ERR = 57
ERROR_BAD_NET_RESP = 58
ERROR_UNEXP_NET_ERR = 59
ERROR_BAD_REM_ADAP = 60
ERROR_PRINTQ_FULL = 61
ERROR_NO_SPOOL_SPACE = 62
ERROR_PRINT_CANCELLED = 63
ERROR_NETNAME_DELETED = 64
ERROR_NETWORK_ACCESS_DENIED = 65
ERROR_BAD_DEV_TYPE = 66
ERROR_BAD_NET_NAME = 67
ERROR_TOO_MANY_NAMES = 68
ERROR_TOO_MANY_SESS = 69
ERROR_SHARING_PAUSED = 70
ERROR_REQ_NOT_ACCEP = 71
ERROR_REDIR_PAUSED = 72
ERROR_FILE_EXISTS = 80
ERROR_CANNOT_MAKE = 82
ERROR_FAIL_I24 = 83
ERROR_OUT_OF_STRUCTURES = 84
ERROR_ALREADY_ASSIGNED = 85
ERROR_INVALID_PASSWORD = 86
ERROR_INVALID_PARAMETER = 87
ERROR_NET_WRITE_FAULT = 88
ERROR_NO_PROC_SLOTS = 89
ERROR_TOO_MANY_SEMAPHORES = 100
ERROR_EXCL_SEM_ALREADY_OWNED = 101
ERROR_SEM_IS_SET = 102
ERROR_TOO_MANY_SEM_REQUESTS = 103
ERROR_INVALID_AT_INTERRUPT_TIME = 104
ERROR_SEM_OWNER_DIED = 105
ERROR_SEM_USER_LIMIT = 106
ERROR_DISK_CHANGE = 107
ERROR_DRIVE_LOCKED = 108
ERROR_BROKEN_PIPE = 109
ERROR_OPEN_FAILED = 110
ERROR_BUFFER_OVERFLOW = 111
ERROR_DISK_FULL = 112
ERROR_NO_MORE_SEARCH_HANDLES = 113
ERROR_INVALID_TARGET_HANDLE = 114
ERROR_INVALID_CATEGORY = 117
ERROR_INVALID_VERIFY_SWITCH = 118
ERROR_BAD_DRIVER_LEVEL = 119
ERROR_CALL_NOT_IMPLEMENTED = 120
ERROR_SEM_TIMEOUT = 121
ERROR_INSUFFICIENT_BUFFER = 122
ERROR_INVALID_NAME = 123
ERROR_INVALID_LEVEL = 124
ERROR_NO_VOLUME_LABEL = 125
ERROR_MOD_NOT_FOUND = 126
ERROR_PROC_NOT_FOUND = 127
ERROR_WAIT_NO_CHILDREN = 128
ERROR_CHILD_NOT_COMPLETE = 129
ERROR_DIRECT_ACCESS_HANDLE = 130
ERROR_NEGATIVE_SEEK = 131
ERROR_SEEK_ON_DEVICE = 132
ERROR_IS_JOIN_TARGET = 133
ERROR_IS_JOINED = 134
ERROR_IS_SUBSTED = 135
ERROR_NOT_JOINED = 136
ERROR_NOT_SUBSTED = 137
ERROR_JOIN_TO_JOIN = 138
ERROR_SUBST_TO_SUBST = 139
ERROR_JOIN_TO_SUBST = 140
ERROR_SUBST_TO_JOIN = 141
ERROR_BUSY_DRIVE = 142
ERROR_SAME_DRIVE = 143
ERROR_DIR_NOT_ROOT = 144
ERROR_DIR_NOT_EMPTY = 145
ERROR_IS_SUBST_PATH = 146
ERROR_IS_JOIN_PATH = 147
ERROR_PATH_BUSY = 148
ERROR_IS_SUBST_TARGET = 149
ERROR_SYSTEM_TRACE = 150
ERROR_INVALID_EVENT_COUNT = 151
ERROR_TOO_MANY_MUXWAITERS = 152
ERROR_INVALID_LIST_FORMAT = 153
ERROR_LABEL_TOO_LONG = 154
ERROR_TOO_MANY_TCBS = 155
ERROR_SIGNAL_REFUSED = 156
ERROR_DISCARDED = 157
ERROR_NOT_LOCKED = 158
ERROR_BAD_THREADID_ADDR = 159
ERROR_BAD_ARGUMENTS = 160
ERROR_BAD_PATHNAME = 161
ERROR_SIGNAL_PENDING = 162
ERROR_MAX_THRDS_REACHED = 164
ERROR_LOCK_FAILED = 167
ERROR_BUSY = 170
ERROR_CANCEL_VIOLATION = 173
ERROR_ATOMIC_LOCKS_NOT_SUPPORTED = 174
ERROR_INVALID_SEGMENT_NUMBER = 180
ERROR_INVALID_ORDINAL = 182
ERROR_ALREADY_EXISTS = 183
ERROR_INVALID_FLAG_NUMBER = 186
ERROR_SEM_NOT_FOUND = 187
ERROR_INVALID_STARTING_CODESEG = 188
ERROR_INVALID_STACKSEG = 189
ERROR_INVALID_MODULETYPE = 190
ERROR_INVALID_EXE_SIGNATURE = 191
ERROR_EXE_MARKED_INVALID = 192
ERROR_BAD_EXE_FORMAT = 193
ERROR_ITERATED_DATA_EXCEEDS_64k = 194
ERROR_INVALID_MINALLOCSIZE = 195
ERROR_DYNLINK_FROM_INVALID_RING = 196
ERROR_IOPL_NOT_ENABLED = 197
ERROR_INVALID_SEGDPL = 198
ERROR_AUTODATASEG_EXCEEDS_64k = 199
ERROR_RING2SEG_MUST_BE_MOVABLE = 200
ERROR_RELOC_CHAIN_XEEDS_SEGLIM = 201
ERROR_INFLOOP_IN_RELOC_CHAIN = 202
ERROR_ENVVAR_NOT_FOUND = 203
ERROR_NO_SIGNAL_SENT = 205
ERROR_FILENAME_EXCED_RANGE = 206
ERROR_RING2_STACK_IN_USE = 207
ERROR_META_EXPANSION_TOO_LONG = 208
ERROR_INVALID_SIGNAL_NUMBER = 209
ERROR_THREAD_1_INACTIVE = 210
ERROR_LOCKED = 212
ERROR_TOO_MANY_MODULES = 214
ERROR_NESTING_NOT_ALLOWED = 215
ERROR_EXE_MACHINE_TYPE_MISMATCH = 216
ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY = 217
ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY = 218
ERROR_FILE_CHECKED_OUT = 220
ERROR_CHECKOUT_REQUIRED = 221
ERROR_BAD_FILE_TYPE = 222
ERROR_FILE_TOO_LARGE = 223
ERROR_FORMS_AUTH_REQUIRED = 224
ERROR_VIRUS_INFECTED = 225
ERROR_VIRUS_DELETED = 226
ERROR_PIPE_LOCAL = 229
ERROR_BAD_PIPE = 230
ERROR_PIPE_BUSY = 231
ERROR_NO_DATA = 232
ERROR_PIPE_NOT_CONNECTED = 233
ERROR_MORE_DATA = 234
ERROR_VC_DISCONNECTED = 240
ERROR_INVALID_EA_NAME = 254
ERROR_EA_LIST_INCONSISTENT = 255
WAIT_TIMEOUT = 258
ERROR_NO_MORE_ITEMS = 259
ERROR_CANNOT_COPY = 266
ERROR_DIRECTORY = 267
ERROR_EAS_DIDNT_FIT = 275
ERROR_EA_FILE_CORRUPT = 276
ERROR_EA_TABLE_FULL = 277
ERROR_INVALID_EA_HANDLE = 278
ERROR_EAS_NOT_SUPPORTED = 282
ERROR_NOT_OWNER = 288
ERROR_TOO_MANY_POSTS = 298
ERROR_PARTIAL_COPY = 299
ERROR_OPLOCK_NOT_GRANTED = 300
ERROR_INVALID_OPLOCK_PROTOCOL = 301
ERROR_DISK_TOO_FRAGMENTED = 302
ERROR_DELETE_PENDING = 303
ERROR_MR_MID_NOT_FOUND = 317
ERROR_SCOPE_NOT_FOUND = 318
ERROR_FAIL_NOACTION_REBOOT = 350
ERROR_FAIL_SHUTDOWN = 351
ERROR_FAIL_RESTART = 352
ERROR_MAX_SESSIONS_REACHED = 353
ERROR_THREAD_MODE_ALREADY_BACKGROUND = 400
ERROR_THREAD_MODE_NOT_BACKGROUND = 401
ERROR_PROCESS_MODE_ALREADY_BACKGROUND = 402
ERROR_PROCESS_MODE_NOT_BACKGROUND = 403
ERROR_INVALID_ADDRESS = 487
ERROR_USER_PROFILE_LOAD = 500
ERROR_ARITHMETIC_OVERFLOW = 534
ERROR_PIPE_CONNECTED = 535
ERROR_PIPE_LISTENING = 536
ERROR_VERIFIER_STOP = 537
ERROR_ABIOS_ERROR = 538
ERROR_WX86_WARNING = 539
ERROR_WX86_ERROR = 540
ERROR_TIMER_NOT_CANCELED = 541
ERROR_UNWIND = 542
ERROR_BAD_STACK = 543
ERROR_INVALID_UNWIND_TARGET = 544
ERROR_INVALID_PORT_ATTRIBUTES = 545
ERROR_PORT_MESSAGE_TOO_LONG = 546
ERROR_INVALID_QUOTA_LOWER = 547
ERROR_DEVICE_ALREADY_ATTACHED = 548
ERROR_INSTRUCTION_MISALIGNMENT = 549
ERROR_PROFILING_NOT_STARTED = 550
ERROR_PROFILING_NOT_STOPPED = 551
ERROR_COULD_NOT_INTERPRET = 552
ERROR_PROFILING_AT_LIMIT = 553
ERROR_CANT_WAIT = 554
ERROR_CANT_TERMINATE_SELF = 555
ERROR_UNEXPECTED_MM_CREATE_ERR = 556
ERROR_UNEXPECTED_MM_MAP_ERROR = 557
ERROR_UNEXPECTED_MM_EXTEND_ERR = 558
ERROR_BAD_FUNCTION_TABLE = 559
ERROR_NO_GUID_TRANSLATION = 560
ERROR_INVALID_LDT_SIZE = 561
ERROR_INVALID_LDT_OFFSET = 563
ERROR_INVALID_LDT_DESCRIPTOR = 564
ERROR_TOO_MANY_THREADS = 565
ERROR_THREAD_NOT_IN_PROCESS = 566
ERROR_PAGEFILE_QUOTA_EXCEEDED = 567
ERROR_LOGON_SERVER_CONFLICT = 568
ERROR_SYNCHRONIZATION_REQUIRED = 569
ERROR_NET_OPEN_FAILED = 570
ERROR_IO_PRIVILEGE_FAILED = 571
ERROR_CONTROL_C_EXIT = 572
ERROR_MISSING_SYSTEMFILE = 573
ERROR_UNHANDLED_EXCEPTION = 574
ERROR_APP_INIT_FAILURE = 575
ERROR_PAGEFILE_CREATE_FAILED = 576
ERROR_INVALID_IMAGE_HASH = 577
ERROR_NO_PAGEFILE = 578
ERROR_ILLEGAL_FLOAT_CONTEXT = 579
ERROR_NO_EVENT_PAIR = 580
ERROR_DOMAIN_CTRLR_CONFIG_ERROR = 581
ERROR_ILLEGAL_CHARACTER = 582
ERROR_UNDEFINED_CHARACTER = 583
ERROR_FLOPPY_VOLUME = 584
ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT = 585
ERROR_BACKUP_CONTROLLER = 586
ERROR_MUTANT_LIMIT_EXCEEDED = 587
ERROR_FS_DRIVER_REQUIRED = 588
ERROR_CANNOT_LOAD_REGISTRY_FILE = 589
ERROR_DEBUG_ATTACH_FAILED = 590
ERROR_SYSTEM_PROCESS_TERMINATED = 591
ERROR_DATA_NOT_ACCEPTED = 592
ERROR_VDM_HARD_ERROR = 593
ERROR_DRIVER_CANCEL_TIMEOUT = 594
ERROR_REPLY_MESSAGE_MISMATCH = 595
ERROR_LOST_WRITEBEHIND_DATA = 596
ERROR_CLIENT_SERVER_PARAMETERS_INVALID = 597
ERROR_NOT_TINY_STREAM = 598
ERROR_STACK_OVERFLOW_READ = 599
ERROR_CONVERT_TO_LARGE = 600
ERROR_FOUND_OUT_OF_SCOPE = 601
ERROR_ALLOCATE_BUCKET = 602
ERROR_MARSHALL_OVERFLOW = 603
ERROR_INVALID_VARIANT = 604
ERROR_BAD_COMPRESSION_BUFFER = 605
ERROR_AUDIT_FAILED = 606
ERROR_TIMER_RESOLUTION_NOT_SET = 607
ERROR_INSUFFICIENT_LOGON_INFO = 608
ERROR_BAD_DLL_ENTRYPOINT = 609
ERROR_BAD_SERVICE_ENTRYPOINT = 610
ERROR_IP_ADDRESS_CONFLICT1 = 611
ERROR_IP_ADDRESS_CONFLICT2 = 612
ERROR_REGISTRY_QUOTA_LIMIT = 613
ERROR_NO_CALLBACK_ACTIVE = 614
ERROR_PWD_TOO_SHORT = 615
ERROR_PWD_TOO_RECENT = 616
ERROR_PWD_HISTORY_CONFLICT = 617
ERROR_UNSUPPORTED_COMPRESSION = 618
ERROR_INVALID_HW_PROFILE = 619
ERROR_INVALID_PLUGPLAY_DEVICE_PATH = 620
ERROR_QUOTA_LIST_INCONSISTENT = 621
ERROR_EVALUATION_EXPIRATION = 622
ERROR_ILLEGAL_DLL_RELOCATION = 623
ERROR_DLL_INIT_FAILED_LOGOFF = 624
ERROR_VALIDATE_CONTINUE = 625
ERROR_NO_MORE_MATCHES = 626
ERROR_RANGE_LIST_CONFLICT = 627
ERROR_SERVER_SID_MISMATCH = 628
ERROR_CANT_ENABLE_DENY_ONLY = 629
ERROR_FLOAT_MULTIPLE_FAULTS = 630
ERROR_FLOAT_MULTIPLE_TRAPS = 631
ERROR_NOINTERFACE = 632
ERROR_DRIVER_FAILED_SLEEP = 633
ERROR_CORRUPT_SYSTEM_FILE = 634
ERROR_COMMITMENT_MINIMUM = 635
ERROR_PNP_RESTART_ENUMERATION = 636
ERROR_SYSTEM_IMAGE_BAD_SIGNATURE = 637
ERROR_PNP_REBOOT_REQUIRED = 638
ERROR_INSUFFICIENT_POWER = 639
ERROR_MULTIPLE_FAULT_VIOLATION = 640
ERROR_SYSTEM_SHUTDOWN = 641
ERROR_PORT_NOT_SET = 642
ERROR_DS_VERSION_CHECK_FAILURE = 643
ERROR_RANGE_NOT_FOUND = 644
ERROR_NOT_SAFE_MODE_DRIVER = 646
ERROR_FAILED_DRIVER_ENTRY = 647
ERROR_DEVICE_ENUMERATION_ERROR = 648
ERROR_MOUNT_POINT_NOT_RESOLVED = 649
ERROR_INVALID_DEVICE_OBJECT_PARAMETER = 650
ERROR_MCA_OCCURED = 651
ERROR_DRIVER_DATABASE_ERROR = 652
ERROR_SYSTEM_HIVE_TOO_LARGE = 653
ERROR_DRIVER_FAILED_PRIOR_UNLOAD = 654
ERROR_VOLSNAP_PREPARE_HIBERNATE = 655
ERROR_HIBERNATION_FAILURE = 656
ERROR_FILE_SYSTEM_LIMITATION = 665
ERROR_ASSERTION_FAILURE = 668
ERROR_ACPI_ERROR = 669
ERROR_WOW_ASSERTION = 670
ERROR_PNP_BAD_MPS_TABLE = 671
ERROR_PNP_TRANSLATION_FAILED = 672
ERROR_PNP_IRQ_TRANSLATION_FAILED = 673
ERROR_PNP_INVALID_ID = 674
ERROR_WAKE_SYSTEM_DEBUGGER = 675
ERROR_HANDLES_CLOSED = 676
ERROR_EXTRANEOUS_INFORMATION = 677
ERROR_RXACT_COMMIT_NECESSARY = 678
ERROR_MEDIA_CHECK = 679
ERROR_GUID_SUBSTITUTION_MADE = 680
ERROR_STOPPED_ON_SYMLINK = 681
ERROR_LONGJUMP = 682
ERROR_PLUGPLAY_QUERY_VETOED = 683
ERROR_UNWIND_CONSOLIDATE = 684
ERROR_REGISTRY_HIVE_RECOVERED = 685
ERROR_DLL_MIGHT_BE_INSECURE = 686
ERROR_DLL_MIGHT_BE_INCOMPATIBLE = 687
ERROR_DBG_EXCEPTION_NOT_HANDLED = 688
ERROR_DBG_REPLY_LATER = 689
ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE = 690
ERROR_DBG_TERMINATE_THREAD = 691
ERROR_DBG_TERMINATE_PROCESS = 692
ERROR_DBG_CONTROL_C = 693
ERROR_DBG_PRINTEXCEPTION_C = 694
ERROR_DBG_RIPEXCEPTION = 695
ERROR_DBG_CONTROL_BREAK = 696
ERROR_DBG_COMMAND_EXCEPTION = 697
ERROR_OBJECT_NAME_EXISTS = 698
ERROR_THREAD_WAS_SUSPENDED = 699
ERROR_IMAGE_NOT_AT_BASE = 700
ERROR_RXACT_STATE_CREATED = 701
ERROR_SEGMENT_NOTIFICATION = 702
ERROR_BAD_CURRENT_DIRECTORY = 703
ERROR_FT_READ_RECOVERY_FROM_BACKUP = 704
ERROR_FT_WRITE_RECOVERY = 705
ERROR_IMAGE_MACHINE_TYPE_MISMATCH = 706
ERROR_RECEIVE_PARTIAL = 707
ERROR_RECEIVE_EXPEDITED = 708
ERROR_RECEIVE_PARTIAL_EXPEDITED = 709
ERROR_EVENT_DONE = 710
ERROR_EVENT_PENDING = 711
ERROR_CHECKING_FILE_SYSTEM = 712
ERROR_FATAL_APP_EXIT = 713
ERROR_PREDEFINED_HANDLE = 714
ERROR_WAS_UNLOCKED = 715
ERROR_SERVICE_NOTIFICATION = 716
ERROR_WAS_LOCKED = 717
ERROR_LOG_HARD_ERROR = 718
ERROR_ALREADY_WIN32 = 719
ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE = 720
ERROR_NO_YIELD_PERFORMED = 721
ERROR_TIMER_RESUME_IGNORED = 722
ERROR_ARBITRATION_UNHANDLED = 723
ERROR_CARDBUS_NOT_SUPPORTED = 724
ERROR_MP_PROCESSOR_MISMATCH = 725
ERROR_HIBERNATED = 726
ERROR_RESUME_HIBERNATION = 727
ERROR_FIRMWARE_UPDATED = 728
ERROR_DRIVERS_LEAKING_LOCKED_PAGES = 729
ERROR_WAKE_SYSTEM = 730
ERROR_WAIT_1 = 731
ERROR_WAIT_2 = 732
ERROR_WAIT_3 = 733
ERROR_WAIT_63 = 734
ERROR_ABANDONED_WAIT_0 = 735
ERROR_ABANDONED_WAIT_63 = 736
ERROR_USER_APC = 737
ERROR_KERNEL_APC = 738
ERROR_ALERTED = 739
ERROR_ELEVATION_REQUIRED = 740
ERROR_REPARSE = 741
ERROR_OPLOCK_BREAK_IN_PROGRESS = 742
ERROR_VOLUME_MOUNTED = 743
ERROR_RXACT_COMMITTED = 744
ERROR_NOTIFY_CLEANUP = 745
ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED = 746
ERROR_PAGE_FAULT_TRANSITION = 747
ERROR_PAGE_FAULT_DEMAND_ZERO = 748
ERROR_PAGE_FAULT_COPY_ON_WRITE = 749
ERROR_PAGE_FAULT_GUARD_PAGE = 750
ERROR_PAGE_FAULT_PAGING_FILE = 751
ERROR_CACHE_PAGE_LOCKED = 752
ERROR_CRASH_DUMP = 753
ERROR_BUFFER_ALL_ZEROS = 754
ERROR_REPARSE_OBJECT = 755
ERROR_RESOURCE_REQUIREMENTS_CHANGED = 756
ERROR_TRANSLATION_COMPLETE = 757
ERROR_NOTHING_TO_TERMINATE = 758
ERROR_PROCESS_NOT_IN_JOB = 759
ERROR_PROCESS_IN_JOB = 760
ERROR_VOLSNAP_HIBERNATE_READY = 761
ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY = 762
ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED = 763
ERROR_INTERRUPT_STILL_CONNECTED = 764
ERROR_WAIT_FOR_OPLOCK = 765
ERROR_DBG_EXCEPTION_HANDLED = 766
ERROR_DBG_CONTINUE = 767
ERROR_CALLBACK_POP_STACK = 768
ERROR_COMPRESSION_DISABLED = 769
ERROR_CANTFETCHBACKWARDS = 770
ERROR_CANTSCROLLBACKWARDS = 771
ERROR_ROWSNOTRELEASED = 772
ERROR_BAD_ACCESSOR_FLAGS = 773
ERROR_ERRORS_ENCOUNTERED = 774
ERROR_NOT_CAPABLE = 775
ERROR_REQUEST_OUT_OF_SEQUENCE = 776
ERROR_VERSION_PARSE_ERROR = 777
ERROR_BADSTARTPOSITION = 778
ERROR_MEMORY_HARDWARE = 779
ERROR_DISK_REPAIR_DISABLED = 780
ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE = 781
ERROR_SYSTEM_POWERSTATE_TRANSITION = 782
ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION = 783
ERROR_MCA_EXCEPTION = 784
ERROR_ACCESS_AUDIT_BY_POLICY = 785
ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY = 786
ERROR_ABANDON_HIBERFILE = 787
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED = 788
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR = 789
ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR = 790
ERROR_BAD_MCFG_TABLE = 791
ERROR_EA_ACCESS_DENIED = 994
ERROR_OPERATION_ABORTED = 995
ERROR_IO_INCOMPLETE = 996
ERROR_IO_PENDING = 997
ERROR_NOACCESS = 998
ERROR_SWAPERROR = 999
ERROR_STACK_OVERFLOW = 1001
ERROR_INVALID_MESSAGE = 1002
ERROR_CAN_NOT_COMPLETE = 1003
ERROR_INVALID_FLAGS = 1004
ERROR_UNRECOGNIZED_VOLUME = 1005
ERROR_FILE_INVALID = 1006
ERROR_FULLSCREEN_MODE = 1007
ERROR_NO_TOKEN = 1008
ERROR_BADDB = 1009
ERROR_BADKEY = 1010
ERROR_CANTOPEN = 1011
ERROR_CANTREAD = 1012
ERROR_CANTWRITE = 1013
ERROR_REGISTRY_RECOVERED = 1014
ERROR_REGISTRY_CORRUPT = 1015
ERROR_REGISTRY_IO_FAILED = 1016
ERROR_NOT_REGISTRY_FILE = 1017
ERROR_KEY_DELETED = 1018
ERROR_NO_LOG_SPACE = 1019
ERROR_KEY_HAS_CHILDREN = 1020
ERROR_CHILD_MUST_BE_VOLATILE = 1021
ERROR_NOTIFY_ENUM_DIR = 1022
ERROR_DEPENDENT_SERVICES_RUNNING = 1051
ERROR_INVALID_SERVICE_CONTROL = 1052
ERROR_SERVICE_REQUEST_TIMEOUT = 1053
ERROR_SERVICE_NO_THREAD = 1054
ERROR_SERVICE_DATABASE_LOCKED = 1055
ERROR_SERVICE_ALREADY_RUNNING = 1056
ERROR_INVALID_SERVICE_ACCOUNT = 1057
ERROR_SERVICE_DISABLED = 1058
ERROR_CIRCULAR_DEPENDENCY = 1059
ERROR_SERVICE_DOES_NOT_EXIST = 1060
ERROR_SERVICE_CANNOT_ACCEPT_CTRL = 1061
ERROR_SERVICE_NOT_ACTIVE = 1062
ERROR_FAILED_SERVICE_CONTROLLER_CONNECT = 1063
ERROR_EXCEPTION_IN_SERVICE = 1064
ERROR_DATABASE_DOES_NOT_EXIST = 1065
ERROR_SERVICE_SPECIFIC_ERROR = 1066
ERROR_PROCESS_ABORTED = 1067
ERROR_SERVICE_DEPENDENCY_FAIL = 1068
ERROR_SERVICE_LOGON_FAILED = 1069
ERROR_SERVICE_START_HANG = 1070
ERROR_INVALID_SERVICE_LOCK = 1071
ERROR_SERVICE_MARKED_FOR_DELETE = 1072
ERROR_SERVICE_EXISTS = 1073
ERROR_ALREADY_RUNNING_LKG = 1074
ERROR_SERVICE_DEPENDENCY_DELETED = 1075
ERROR_BOOT_ALREADY_ACCEPTED = 1076
ERROR_SERVICE_NEVER_STARTED = 1077
ERROR_DUPLICATE_SERVICE_NAME = 1078
ERROR_DIFFERENT_SERVICE_ACCOUNT = 1079
ERROR_CANNOT_DETECT_DRIVER_FAILURE = 1080
ERROR_CANNOT_DETECT_PROCESS_ABORT = 1081
ERROR_NO_RECOVERY_PROGRAM = 1082
ERROR_SERVICE_NOT_IN_EXE = 1083
ERROR_NOT_SAFEBOOT_SERVICE = 1084
ERROR_END_OF_MEDIA = 1100
ERROR_FILEMARK_DETECTED = 1101
ERROR_BEGINNING_OF_MEDIA = 1102
ERROR_SETMARK_DETECTED = 1103
ERROR_NO_DATA_DETECTED = 1104
ERROR_PARTITION_FAILURE = 1105
ERROR_INVALID_BLOCK_LENGTH = 1106
ERROR_DEVICE_NOT_PARTITIONED = 1107
ERROR_UNABLE_TO_LOCK_MEDIA = 1108
ERROR_UNABLE_TO_UNLOAD_MEDIA = 1109
ERROR_MEDIA_CHANGED = 1110
ERROR_BUS_RESET = 1111
ERROR_NO_MEDIA_IN_DRIVE = 1112
ERROR_NO_UNICODE_TRANSLATION = 1113
ERROR_DLL_INIT_FAILED = 1114
ERROR_SHUTDOWN_IN_PROGRESS = 1115
ERROR_NO_SHUTDOWN_IN_PROGRESS = 1116
ERROR_IO_DEVICE = 1117
ERROR_SERIAL_NO_DEVICE = 1118
ERROR_IRQ_BUSY = 1119
ERROR_MORE_WRITES = 1120
ERROR_COUNTER_TIMEOUT = 1121
ERROR_FLOPPY_ID_MARK_NOT_FOUND = 1122
ERROR_FLOPPY_WRONG_CYLINDER = 1123
ERROR_FLOPPY_UNKNOWN_ERROR = 1124
ERROR_FLOPPY_BAD_REGISTERS = 1125
ERROR_DISK_RECALIBRATE_FAILED = 1126
ERROR_DISK_OPERATION_FAILED = 1127
ERROR_DISK_RESET_FAILED = 1128
ERROR_EOM_OVERFLOW = 1129
ERROR_NOT_ENOUGH_SERVER_MEMORY = 1130
ERROR_POSSIBLE_DEADLOCK = 1131
ERROR_MAPPED_ALIGNMENT = 1132
ERROR_SET_POWER_STATE_VETOED = 1140
ERROR_SET_POWER_STATE_FAILED = 1141
ERROR_TOO_MANY_LINKS = 1142
ERROR_OLD_WIN_VERSION = 1150
ERROR_APP_WRONG_OS = 1151
ERROR_SINGLE_INSTANCE_APP = 1152
ERROR_RMODE_APP = 1153
ERROR_INVALID_DLL = 1154
ERROR_NO_ASSOCIATION = 1155
ERROR_DDE_FAIL = 1156
ERROR_DLL_NOT_FOUND = 1157
ERROR_NO_MORE_USER_HANDLES = 1158
ERROR_MESSAGE_SYNC_ONLY = 1159
ERROR_SOURCE_ELEMENT_EMPTY = 1160
ERROR_DESTINATION_ELEMENT_FULL = 1161
ERROR_ILLEGAL_ELEMENT_ADDRESS = 1162
ERROR_MAGAZINE_NOT_PRESENT = 1163
ERROR_DEVICE_REINITIALIZATION_NEEDED = 1164
ERROR_DEVICE_REQUIRES_CLEANING = 1165
ERROR_DEVICE_DOOR_OPEN = 1166
ERROR_DEVICE_NOT_CONNECTED = 1167
ERROR_NOT_FOUND = 1168
ERROR_NO_MATCH = 1169
ERROR_SET_NOT_FOUND = 1170
ERROR_POINT_NOT_FOUND = 1171
ERROR_NO_TRACKING_SERVICE = 1172
ERROR_NO_VOLUME_ID = 1173
ERROR_CONNECTED_OTHER_PASSWORD = 2108
ERROR_BAD_USERNAME = 2202
ERROR_NOT_CONNECTED = 2250
ERROR_OPEN_FILES = 2401
ERROR_ACTIVE_CONNECTIONS = 2402
ERROR_DEVICE_IN_USE = 2404
ERROR_BAD_DEVICE = 1200
ERROR_CONNECTION_UNAVAIL = 1201
ERROR_DEVICE_ALREADY_REMEMBERED = 1202
ERROR_NO_NET_OR_BAD_PATH = 1203
ERROR_BAD_PROVIDER = 1204
ERROR_CANNOT_OPEN_PROFILE = 1205
ERROR_BAD_PROFILE = 1206
ERROR_NOT_CONTAINER = 1207
ERROR_EXTENDED_ERROR = 1208
ERROR_INVALID_GROUPNAME = 1209
ERROR_INVALID_COMPUTERNAME = 1210
ERROR_INVALID_EVENTNAME = 1211
ERROR_INVALID_DOMAINNAME = 1212
ERROR_INVALID_SERVICENAME = 1213
ERROR_INVALID_NETNAME = 1214
ERROR_INVALID_SHARENAME = 1215
ERROR_INVALID_PASSWORDNAME = 1216
ERROR_INVALID_MESSAGENAME = 1217
ERROR_INVALID_MESSAGEDEST = 1218
ERROR_SESSION_CREDENTIAL_CONFLICT = 1219
ERROR_REMOTE_SESSION_LIMIT_EXCEEDED = 1220
ERROR_DUP_DOMAINNAME = 1221
ERROR_NO_NETWORK = 1222
ERROR_CANCELLED = 1223
ERROR_USER_MAPPED_FILE = 1224
ERROR_CONNECTION_REFUSED = 1225
ERROR_GRACEFUL_DISCONNECT = 1226
ERROR_ADDRESS_ALREADY_ASSOCIATED = 1227
ERROR_ADDRESS_NOT_ASSOCIATED = 1228
ERROR_CONNECTION_INVALID = 1229
ERROR_CONNECTION_ACTIVE = 1230
ERROR_NETWORK_UNREACHABLE = 1231
ERROR_HOST_UNREACHABLE = 1232
ERROR_PROTOCOL_UNREACHABLE = 1233
ERROR_PORT_UNREACHABLE = 1234
ERROR_REQUEST_ABORTED = 1235
ERROR_CONNECTION_ABORTED = 1236
ERROR_RETRY = 1237
ERROR_CONNECTION_COUNT_LIMIT = 1238
ERROR_LOGIN_TIME_RESTRICTION = 1239
ERROR_LOGIN_WKSTA_RESTRICTION = 1240
ERROR_INCORRECT_ADDRESS = 1241
ERROR_ALREADY_REGISTERED = 1242
ERROR_SERVICE_NOT_FOUND = 1243
ERROR_NOT_AUTHENTICATED = 1244
ERROR_NOT_LOGGED_ON = 1245
ERROR_CONTINUE = 1246
ERROR_ALREADY_INITIALIZED = 1247
ERROR_NO_MORE_DEVICES = 1248
ERROR_NO_SUCH_SITE = 1249
ERROR_DOMAIN_CONTROLLER_EXISTS = 1250
ERROR_DS_NOT_INSTALLED = 1251
ERROR_NOT_ALL_ASSIGNED = 1300
ERROR_SOME_NOT_MAPPED = 1301
ERROR_NO_QUOTAS_FOR_ACCOUNT = 1302
ERROR_LOCAL_USER_SESSION_KEY = 1303
ERROR_NULL_LM_PASSWORD = 1304
ERROR_UNKNOWN_REVISION = 1305
ERROR_REVISION_MISMATCH = 1306
ERROR_INVALID_OWNER = 1307
ERROR_INVALID_PRIMARY_GROUP = 1308
ERROR_NO_IMPERSONATION_TOKEN = 1309
ERROR_CANT_DISABLE_MANDATORY = 1310
ERROR_NO_LOGON_SERVERS = 1311
ERROR_NO_SUCH_LOGON_SESSION = 1312
ERROR_NO_SUCH_PRIVILEGE = 1313
ERROR_PRIVILEGE_NOT_HELD = 1314
ERROR_INVALID_ACCOUNT_NAME = 1315
ERROR_USER_EXISTS = 1316
ERROR_NO_SUCH_USER = 1317
ERROR_GROUP_EXISTS = 1318
ERROR_NO_SUCH_GROUP = 1319
ERROR_MEMBER_IN_GROUP = 1320
ERROR_MEMBER_NOT_IN_GROUP = 1321
ERROR_LAST_ADMIN = 1322
ERROR_WRONG_PASSWORD = 1323
ERROR_ILL_FORMED_PASSWORD = 1324
ERROR_PASSWORD_RESTRICTION = 1325
ERROR_LOGON_FAILURE = 1326
ERROR_ACCOUNT_RESTRICTION = 1327
ERROR_INVALID_LOGON_HOURS = 1328
ERROR_INVALID_WORKSTATION = 1329
ERROR_PASSWORD_EXPIRED = 1330
ERROR_ACCOUNT_DISABLED = 1331
ERROR_NONE_MAPPED = 1332
ERROR_TOO_MANY_LUIDS_REQUESTED = 1333
ERROR_LUIDS_EXHAUSTED = 1334
ERROR_INVALID_SUB_AUTHORITY = 1335
ERROR_INVALID_ACL = 1336
ERROR_INVALID_SID = 1337
ERROR_INVALID_SECURITY_DESCR = 1338
ERROR_BAD_INHERITANCE_ACL = 1340
ERROR_SERVER_DISABLED = 1341
ERROR_SERVER_NOT_DISABLED = 1342
ERROR_INVALID_ID_AUTHORITY = 1343
ERROR_ALLOTTED_SPACE_EXCEEDED = 1344
ERROR_INVALID_GROUP_ATTRIBUTES = 1345
ERROR_BAD_IMPERSONATION_LEVEL = 1346
ERROR_CANT_OPEN_ANONYMOUS = 1347
ERROR_BAD_VALIDATION_CLASS = 1348
ERROR_BAD_TOKEN_TYPE = 1349
ERROR_NO_SECURITY_ON_OBJECT = 1350
ERROR_CANT_ACCESS_DOMAIN_INFO = 1351
ERROR_INVALID_SERVER_STATE = 1352
ERROR_INVALID_DOMAIN_STATE = 1353
ERROR_INVALID_DOMAIN_ROLE = 1354
ERROR_NO_SUCH_DOMAIN = 1355
ERROR_DOMAIN_EXISTS = 1356
ERROR_DOMAIN_LIMIT_EXCEEDED = 1357
ERROR_INTERNAL_DB_CORRUPTION = 1358
ERROR_INTERNAL_ERROR = 1359
ERROR_GENERIC_NOT_MAPPED = 1360
ERROR_BAD_DESCRIPTOR_FORMAT = 1361
ERROR_NOT_LOGON_PROCESS = 1362
ERROR_LOGON_SESSION_EXISTS = 1363
ERROR_NO_SUCH_PACKAGE = 1364
ERROR_BAD_LOGON_SESSION_STATE = 1365
ERROR_LOGON_SESSION_COLLISION = 1366
ERROR_INVALID_LOGON_TYPE = 1367
ERROR_CANNOT_IMPERSONATE = 1368
ERROR_RXACT_INVALID_STATE = 1369
ERROR_RXACT_COMMIT_FAILURE = 1370
ERROR_SPECIAL_ACCOUNT = 1371
ERROR_SPECIAL_GROUP = 1372
ERROR_SPECIAL_USER = 1373
ERROR_MEMBERS_PRIMARY_GROUP = 1374
ERROR_TOKEN_ALREADY_IN_USE = 1375
ERROR_NO_SUCH_ALIAS = 1376
ERROR_MEMBER_NOT_IN_ALIAS = 1377
ERROR_MEMBER_IN_ALIAS = 1378
ERROR_ALIAS_EXISTS = 1379
ERROR_LOGON_NOT_GRANTED = 1380
ERROR_TOO_MANY_SECRETS = 1381
ERROR_SECRET_TOO_LONG = 1382
ERROR_INTERNAL_DB_ERROR = 1383
ERROR_TOO_MANY_CONTEXT_IDS = 1384
ERROR_LOGON_TYPE_NOT_GRANTED = 1385
ERROR_NT_CROSS_ENCRYPTION_REQUIRED = 1386
ERROR_NO_SUCH_MEMBER = 1387
ERROR_INVALID_MEMBER = 1388
ERROR_TOO_MANY_SIDS = 1389
ERROR_LM_CROSS_ENCRYPTION_REQUIRED = 1390
ERROR_NO_INHERITANCE = 1391
ERROR_FILE_CORRUPT = 1392
ERROR_DISK_CORRUPT = 1393
ERROR_NO_USER_SESSION_KEY = 1394
ERROR_LICENSE_QUOTA_EXCEEDED = 1395
ERROR_INVALID_WINDOW_HANDLE = 1400
ERROR_INVALID_MENU_HANDLE = 1401
ERROR_INVALID_CURSOR_HANDLE = 1402
ERROR_INVALID_ACCEL_HANDLE = 1403
ERROR_INVALID_HOOK_HANDLE = 1404
ERROR_INVALID_DWP_HANDLE = 1405
ERROR_TLW_WITH_WSCHILD = 1406
ERROR_CANNOT_FIND_WND_CLASS = 1407
ERROR_WINDOW_OF_OTHER_THREAD = 1408
ERROR_HOTKEY_ALREADY_REGISTERED = 1409
ERROR_CLASS_ALREADY_EXISTS = 1410
ERROR_CLASS_DOES_NOT_EXIST = 1411
ERROR_CLASS_HAS_WINDOWS = 1412
ERROR_INVALID_INDEX = 1413
ERROR_INVALID_ICON_HANDLE = 1414
ERROR_PRIVATE_DIALOG_INDEX = 1415
ERROR_LISTBOX_ID_NOT_FOUND = 1416
ERROR_NO_WILDCARD_CHARACTERS = 1417
ERROR_CLIPBOARD_NOT_OPEN = 1418
ERROR_HOTKEY_NOT_REGISTERED = 1419
ERROR_WINDOW_NOT_DIALOG = 1420
ERROR_CONTROL_ID_NOT_FOUND = 1421
ERROR_INVALID_COMBOBOX_MESSAGE = 1422
ERROR_WINDOW_NOT_COMBOBOX = 1423
ERROR_INVALID_EDIT_HEIGHT = 1424
ERROR_DC_NOT_FOUND = 1425
ERROR_INVALID_HOOK_FILTER = 1426
ERROR_INVALID_FILTER_PROC = 1427
ERROR_HOOK_NEEDS_HMOD = 1428
ERROR_GLOBAL_ONLY_HOOK = 1429
ERROR_JOURNAL_HOOK_SET = 1430
ERROR_HOOK_NOT_INSTALLED = 1431
ERROR_INVALID_LB_MESSAGE = 1432
ERROR_SETCOUNT_ON_BAD_LB = 1433
ERROR_LB_WITHOUT_TABSTOPS = 1434
ERROR_DESTROY_OBJECT_OF_OTHER_THREAD = 1435
ERROR_CHILD_WINDOW_MENU = 1436
ERROR_NO_SYSTEM_MENU = 1437
ERROR_INVALID_MSGBOX_STYLE = 1438
ERROR_INVALID_SPI_VALUE = 1439
ERROR_SCREEN_ALREADY_LOCKED = 1440
ERROR_HWNDS_HAVE_DIFF_PARENT = 1441
ERROR_NOT_CHILD_WINDOW = 1442
ERROR_INVALID_GW_COMMAND = 1443
ERROR_INVALID_THREAD_ID = 1444
ERROR_NON_MDICHILD_WINDOW = 1445
ERROR_POPUP_ALREADY_ACTIVE = 1446
ERROR_NO_SCROLLBARS = 1447
ERROR_INVALID_SCROLLBAR_RANGE = 1448
ERROR_INVALID_SHOWWIN_COMMAND = 1449
ERROR_NO_SYSTEM_RESOURCES = 1450
ERROR_NONPAGED_SYSTEM_RESOURCES = 1451
ERROR_PAGED_SYSTEM_RESOURCES = 1452
ERROR_WORKING_SET_QUOTA = 1453
ERROR_PAGEFILE_QUOTA = 1454
ERROR_COMMITMENT_LIMIT = 1455
ERROR_MENU_ITEM_NOT_FOUND = 1456
ERROR_INVALID_KEYBOARD_HANDLE = 1457
ERROR_HOOK_TYPE_NOT_ALLOWED = 1458
ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION = 1459
ERROR_TIMEOUT = 1460
ERROR_INVALID_MONITOR_HANDLE = 1461
ERROR_INCORRECT_SIZE = 1462
ERROR_SYMLINK_CLASS_DISABLED = 1463
ERROR_SYMLINK_NOT_SUPPORTED = 1464
ERROR_XML_PARSE_ERROR = 1465
ERROR_XMLDSIG_ERROR = 1466
ERROR_RESTART_APPLICATION = 1467
ERROR_WRONG_COMPARTMENT = 1468
ERROR_AUTHIP_FAILURE = 1469
ERROR_EVENTLOG_FILE_CORRUPT = 1500
ERROR_EVENTLOG_CANT_START = 1501
ERROR_LOG_FILE_FULL = 1502
ERROR_EVENTLOG_FILE_CHANGED = 1503
ERROR_EVENTLOG_FILE_CORRUPT = 1500
ERROR_EVENTLOG_CANT_START = 1501
ERROR_LOG_FILE_FULL = 1502
ERROR_INSTALL_SERVICE = 1601
ERROR_INSTALL_USEREXIT = 1602
ERROR_INSTALL_FAILURE = 1603
ERROR_INSTALL_SUSPEND = 1604
ERROR_UNKNOWN_PRODUCT = 1605
ERROR_UNKNOWN_FEATURE = 1606
ERROR_UNKNOWN_COMPONENT = 1607
ERROR_UNKNOWN_PROPERTY = 1608
ERROR_INVALID_HANDLE_STATE = 1609
ERROR_BAD_CONFIGURATION = 1610
ERROR_INDEX_ABSENT = 1611
ERROR_INSTALL_SOURCE_ABSENT = 1612
ERROR_BAD_DATABASE_VERSION = 1613
ERROR_PRODUCT_UNINSTALLED = 1614
ERROR_BAD_QUERY_SYNTAX = 1615
ERROR_INVALID_FIELD = 1616
ERROR_DEVICE_REMOVED = 1617
ERROR_INSTALL_ALREADY_RUNNING = 1618
ERROR_INSTALL_PACKAGE_OPEN_FAILED = 1619
ERROR_INSTALL_PACKAGE_INVALID = 1620
ERROR_INSTALL_UI_FAILURE = 1621
ERROR_INSTALL_LOG_FAILURE = 1622
ERROR_INSTALL_LANGUAGE_UNSUPPORTED = 1623
ERROR_INSTALL_TRANSFORM_FAILURE = 1624
ERROR_INSTALL_PACKAGE_REJECTED = 1625
ERROR_FUNCTION_NOT_CALLED = 1626
ERROR_FUNCTION_FAILED = 1627
ERROR_INVALID_TABLE = 1628
ERROR_DATATYPE_MISMATCH = 1629
ERROR_UNSUPPORTED_TYPE = 1630
ERROR_CREATE_FAILED = 1631
ERROR_INSTALL_TEMP_UNWRITABLE = 1632
ERROR_INSTALL_PLATFORM_UNSUPPORTED = 1633
ERROR_INSTALL_NOTUSED = 1634
ERROR_PATCH_PACKAGE_OPEN_FAILED = 1635
ERROR_PATCH_PACKAGE_INVALID = 1636
ERROR_PATCH_PACKAGE_UNSUPPORTED = 1637
ERROR_PRODUCT_VERSION = 1638
ERROR_INVALID_COMMAND_LINE = 1639
ERROR_INSTALL_REMOTE_DISALLOWED = 1640
ERROR_SUCCESS_REBOOT_INITIATED = 1641
ERROR_PATCH_TARGET_NOT_FOUND = 1642
ERROR_PATCH_PACKAGE_REJECTED = 1643
ERROR_INSTALL_TRANSFORM_REJECTED = 1644
ERROR_INSTALL_REMOTE_PROHIBITED = 1645
ERROR_PATCH_REMOVAL_UNSUPPORTED = 1646
ERROR_UNKNOWN_PATCH = 1647
ERROR_PATCH_NO_SEQUENCE = 1648
ERROR_PATCH_REMOVAL_DISALLOWED = 1649
ERROR_INVALID_PATCH_XML = 1650
ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT = 1651
ERROR_INSTALL_SERVICE_SAFEBOOT = 1652
RPC_S_INVALID_STRING_BINDING = 1700
RPC_S_WRONG_KIND_OF_BINDING = 1701
RPC_S_INVALID_BINDING = 1702
RPC_S_PROTSEQ_NOT_SUPPORTED = 1703
RPC_S_INVALID_RPC_PROTSEQ = 1704
RPC_S_INVALID_STRING_UUID = 1705
RPC_S_INVALID_ENDPOINT_FORMAT = 1706
RPC_S_INVALID_NET_ADDR = 1707
RPC_S_NO_ENDPOINT_FOUND = 1708
RPC_S_INVALID_TIMEOUT = 1709
RPC_S_OBJECT_NOT_FOUND = 1710
RPC_S_ALREADY_REGISTERED = 1711
RPC_S_TYPE_ALREADY_REGISTERED = 1712
RPC_S_ALREADY_LISTENING = 1713
RPC_S_NO_PROTSEQS_REGISTERED = 1714
RPC_S_NOT_LISTENING = 1715
RPC_S_UNKNOWN_MGR_TYPE = 1716
RPC_S_UNKNOWN_IF = 1717
RPC_S_NO_BINDINGS = 1718
RPC_S_NO_PROTSEQS = 1719
RPC_S_CANT_CREATE_ENDPOINT = 1720
RPC_S_OUT_OF_RESOURCES = 1721
RPC_S_SERVER_UNAVAILABLE = 1722
RPC_S_SERVER_TOO_BUSY = 1723
RPC_S_INVALID_NETWORK_OPTIONS = 1724
RPC_S_NO_CALL_ACTIVE = 1725
RPC_S_CALL_FAILED = 1726
RPC_S_CALL_FAILED_DNE = 1727
RPC_S_PROTOCOL_ERROR = 1728
RPC_S_PROXY_ACCESS_DENIED = 1729
RPC_S_UNSUPPORTED_TRANS_SYN = 1730
RPC_S_UNSUPPORTED_TYPE = 1732
RPC_S_INVALID_TAG = 1733
RPC_S_INVALID_BOUND = 1734
RPC_S_NO_ENTRY_NAME = 1735
RPC_S_INVALID_NAME_SYNTAX = 1736
RPC_S_UNSUPPORTED_NAME_SYNTAX = 1737
RPC_S_UUID_NO_ADDRESS = 1739
RPC_S_DUPLICATE_ENDPOINT = 1740
RPC_S_UNKNOWN_AUTHN_TYPE = 1741
RPC_S_MAX_CALLS_TOO_SMALL = 1742
RPC_S_STRING_TOO_LONG = 1743
RPC_S_PROTSEQ_NOT_FOUND = 1744
RPC_S_PROCNUM_OUT_OF_RANGE = 1745
RPC_S_BINDING_HAS_NO_AUTH = 1746
RPC_S_UNKNOWN_AUTHN_SERVICE = 1747
RPC_S_UNKNOWN_AUTHN_LEVEL = 1748
RPC_S_INVALID_AUTH_IDENTITY = 1749
RPC_S_UNKNOWN_AUTHZ_SERVICE = 1750
EPT_S_INVALID_ENTRY = 1751
EPT_S_CANT_PERFORM_OP = 1752
EPT_S_NOT_REGISTERED = 1753
RPC_S_NOTHING_TO_EXPORT = 1754
RPC_S_INCOMPLETE_NAME = 1755
RPC_S_INVALID_VERS_OPTION = 1756
RPC_S_NO_MORE_MEMBERS = 1757
RPC_S_NOT_ALL_OBJS_UNEXPORTED = 1758
RPC_S_INTERFACE_NOT_FOUND = 1759
RPC_S_ENTRY_ALREADY_EXISTS = 1760
RPC_S_ENTRY_NOT_FOUND = 1761
RPC_S_NAME_SERVICE_UNAVAILABLE = 1762
RPC_S_INVALID_NAF_ID = 1763
RPC_S_CANNOT_SUPPORT = 1764
RPC_S_NO_CONTEXT_AVAILABLE = 1765
RPC_S_INTERNAL_ERROR = 1766
RPC_S_ZERO_DIVIDE = 1767
RPC_S_ADDRESS_ERROR = 1768
RPC_S_FP_DIV_ZERO = 1769
RPC_S_FP_UNDERFLOW = 1770
RPC_S_FP_OVERFLOW = 1771
RPC_X_NO_MORE_ENTRIES = 1772
RPC_X_SS_CHAR_TRANS_OPEN_FAIL = 1773
RPC_X_SS_CHAR_TRANS_SHORT_FILE = 1774
RPC_X_SS_IN_NULL_CONTEXT = 1775
RPC_X_SS_CONTEXT_DAMAGED = 1777
RPC_X_SS_HANDLES_MISMATCH = 1778
RPC_X_SS_CANNOT_GET_CALL_HANDLE = 1779
RPC_X_NULL_REF_POINTER = 1780
RPC_X_ENUM_VALUE_OUT_OF_RANGE = 1781
RPC_X_BYTE_COUNT_TOO_SMALL = 1782
RPC_X_BAD_STUB_DATA = 1783
ERROR_INVALID_USER_BUFFER = 1784
ERROR_UNRECOGNIZED_MEDIA = 1785
ERROR_NO_TRUST_LSA_SECRET = 1786
ERROR_NO_TRUST_SAM_ACCOUNT = 1787
ERROR_TRUSTED_DOMAIN_FAILURE = 1788
ERROR_TRUSTED_RELATIONSHIP_FAILURE = 1789
ERROR_TRUST_FAILURE = 1790
RPC_S_CALL_IN_PROGRESS = 1791
ERROR_NETLOGON_NOT_STARTED = 1792
ERROR_ACCOUNT_EXPIRED = 1793
ERROR_REDIRECTOR_HAS_OPEN_HANDLES = 1794
ERROR_PRINTER_DRIVER_ALREADY_INSTALLED = 1795
ERROR_UNKNOWN_PORT = 1796
ERROR_UNKNOWN_PRINTER_DRIVER = 1797
ERROR_UNKNOWN_PRINTPROCESSOR = 1798
ERROR_INVALID_SEPARATOR_FILE = 1799
ERROR_INVALID_PRIORITY = 1800
ERROR_INVALID_PRINTER_NAME = 1801
ERROR_PRINTER_ALREADY_EXISTS = 1802
ERROR_INVALID_PRINTER_COMMAND = 1803
ERROR_INVALID_DATATYPE = 1804
ERROR_INVALID_ENVIRONMENT = 1805
RPC_S_NO_MORE_BINDINGS = 1806
ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT = 1807
ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT = 1808
ERROR_NOLOGON_SERVER_TRUST_ACCOUNT = 1809
ERROR_DOMAIN_TRUST_INCONSISTENT = 1810
ERROR_SERVER_HAS_OPEN_HANDLES = 1811
ERROR_RESOURCE_DATA_NOT_FOUND = 1812
ERROR_RESOURCE_TYPE_NOT_FOUND = 1813
ERROR_RESOURCE_NAME_NOT_FOUND = 1814
ERROR_RESOURCE_LANG_NOT_FOUND = 1815
ERROR_NOT_ENOUGH_QUOTA = 1816
RPC_S_NO_INTERFACES = 1817
RPC_S_CALL_CANCELLED = 1818
RPC_S_BINDING_INCOMPLETE = 1819
RPC_S_COMM_FAILURE = 1820
RPC_S_UNSUPPORTED_AUTHN_LEVEL = 1821
RPC_S_NO_PRINC_NAME = 1822
RPC_S_NOT_RPC_ERROR = 1823
RPC_S_UUID_LOCAL_ONLY = 1824
RPC_S_SEC_PKG_ERROR = 1825
RPC_S_NOT_CANCELLED = 1826
RPC_X_INVALID_ES_ACTION = 1827
RPC_X_WRONG_ES_VERSION = 1828
RPC_X_WRONG_STUB_VERSION = 1829
RPC_X_INVALID_PIPE_OBJECT = 1830
RPC_X_WRONG_PIPE_ORDER = 1831
RPC_X_WRONG_PIPE_VERSION = 1832
RPC_S_GROUP_MEMBER_NOT_FOUND = 1898
EPT_S_CANT_CREATE = 1899
RPC_S_INVALID_OBJECT = 1900
ERROR_INVALID_TIME = 1901
ERROR_INVALID_FORM_NAME = 1902
ERROR_INVALID_FORM_SIZE = 1903
ERROR_ALREADY_WAITING = 1904
ERROR_PRINTER_DELETED = 1905
ERROR_INVALID_PRINTER_STATE = 1906
ERROR_PASSWORD_MUST_CHANGE = 1907
ERROR_DOMAIN_CONTROLLER_NOT_FOUND = 1908
ERROR_ACCOUNT_LOCKED_OUT = 1909
OR_INVALID_OXID = 1910
OR_INVALID_OID = 1911
OR_INVALID_SET = 1912
RPC_S_SEND_INCOMPLETE = 1913
RPC_S_INVALID_ASYNC_HANDLE = 1914
RPC_S_INVALID_ASYNC_CALL = 1915
RPC_X_PIPE_CLOSED = 1916
RPC_X_PIPE_DISCIPLINE_ERROR = 1917
RPC_X_PIPE_EMPTY = 1918
ERROR_NO_SITENAME = 1919
ERROR_CANT_ACCESS_FILE = 1920
ERROR_CANT_RESOLVE_FILENAME = 1921
RPC_S_ENTRY_TYPE_MISMATCH = 1922
RPC_S_NOT_ALL_OBJS_EXPORTED = 1923
RPC_S_INTERFACE_NOT_EXPORTED = 1924
RPC_S_PROFILE_NOT_ADDED = 1925
RPC_S_PRF_ELT_NOT_ADDED = 1926
RPC_S_PRF_ELT_NOT_REMOVED = 1927
RPC_S_GRP_ELT_NOT_ADDED = 1928
RPC_S_GRP_ELT_NOT_REMOVED = 1929
ERROR_KM_DRIVER_BLOCKED = 1930
ERROR_CONTEXT_EXPIRED = 1931
ERROR_PER_USER_TRUST_QUOTA_EXCEEDED = 1932
ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED = 1933
ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED = 1934
ERROR_AUTHENTICATION_FIREWALL_FAILED = 1935
ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED = 1936
ERROR_NTLM_BLOCKED = 1937
ERROR_INVALID_PIXEL_FORMAT = 2000
ERROR_BAD_DRIVER = 2001
ERROR_INVALID_WINDOW_STYLE = 2002
ERROR_METAFILE_NOT_SUPPORTED = 2003
ERROR_TRANSFORM_NOT_SUPPORTED = 2004
ERROR_CLIPPING_NOT_SUPPORTED = 2005
ERROR_INVALID_CMM = 2010
ERROR_INVALID_PROFILE = 2011
ERROR_TAG_NOT_FOUND = 2012
ERROR_TAG_NOT_PRESENT = 2013
ERROR_DUPLICATE_TAG = 2014
ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE = 2015
ERROR_PROFILE_NOT_FOUND = 2016
ERROR_INVALID_COLORSPACE = 2017
ERROR_ICM_NOT_ENABLED = 2018
ERROR_DELETING_ICM_XFORM = 2019
ERROR_INVALID_TRANSFORM = 2020
ERROR_COLORSPACE_MISMATCH = 2021
ERROR_INVALID_COLORINDEX = 2022
ERROR_PROFILE_DOES_NOT_MATCH_DEVICE = 2023
ERROR_CONNECTED_OTHER_PASSWORD = 2108
ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT = 2109
ERROR_BAD_USERNAME = 2202
ERROR_NOT_CONNECTED = 2250
ERROR_INVALID_CMM = 2300
ERROR_INVALID_PROFILE = 2301
ERROR_TAG_NOT_FOUND = 2302
ERROR_TAG_NOT_PRESENT = 2303
ERROR_DUPLICATE_TAG = 2304
ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE = 2305
ERROR_PROFILE_NOT_FOUND = 2306
ERROR_INVALID_COLORSPACE = 2307
ERROR_ICM_NOT_ENABLED = 2308
ERROR_DELETING_ICM_XFORM = 2309
ERROR_INVALID_TRANSFORM = 2310
ERROR_OPEN_FILES = 2401
ERROR_ACTIVE_CONNECTIONS = 2402
ERROR_DEVICE_IN_USE = 2404
ERROR_UNKNOWN_PRINT_MONITOR = 3000
ERROR_PRINTER_DRIVER_IN_USE = 3001
ERROR_SPOOL_FILE_NOT_FOUND = 3002
ERROR_SPL_NO_STARTDOC = 3003
ERROR_SPL_NO_ADDJOB = 3004
ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED = 3005
ERROR_PRINT_MONITOR_ALREADY_INSTALLED = 3006
ERROR_INVALID_PRINT_MONITOR = 3007
ERROR_PRINT_MONITOR_IN_USE = 3008
ERROR_PRINTER_HAS_JOBS_QUEUED = 3009
ERROR_SUCCESS_REBOOT_REQUIRED = 3010
ERROR_SUCCESS_RESTART_REQUIRED = 3011
ERROR_PRINTER_NOT_FOUND = 3012
ERROR_PRINTER_DRIVER_WARNED = 3013
ERROR_PRINTER_DRIVER_BLOCKED = 3014
ERROR_PRINTER_DRIVER_PACKAGE_IN_USE = 3015
ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND = 3016
ERROR_FAIL_REBOOT_REQUIRED = 3017
ERROR_FAIL_REBOOT_INITIATED = 3018
ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED = 3019
ERROR_PRINT_JOB_RESTART_REQUIRED = 3020
ERROR_IO_REISSUE_AS_CACHED = 3950
ERROR_WINS_INTERNAL = 4000
ERROR_CAN_NOT_DEL_LOCAL_WINS = 4001
ERROR_STATIC_INIT = 4002
ERROR_INC_BACKUP = 4003
ERROR_FULL_BACKUP = 4004
ERROR_REC_NON_EXISTENT = 4005
ERROR_RPL_NOT_ALLOWED = 4006
ERROR_DHCP_ADDRESS_CONFLICT = 4100
ERROR_WMI_GUID_NOT_FOUND = 4200
ERROR_WMI_INSTANCE_NOT_FOUND = 4201
ERROR_WMI_ITEMID_NOT_FOUND = 4202
ERROR_WMI_TRY_AGAIN = 4203
ERROR_WMI_DP_NOT_FOUND = 4204
ERROR_WMI_UNRESOLVED_INSTANCE_REF = 4205
ERROR_WMI_ALREADY_ENABLED = 4206
ERROR_WMI_GUID_DISCONNECTED = 4207
ERROR_WMI_SERVER_UNAVAILABLE = 4208
ERROR_WMI_DP_FAILED = 4209
ERROR_WMI_INVALID_MOF = 4210
ERROR_WMI_INVALID_REGINFO = 4211
ERROR_WMI_ALREADY_DISABLED = 4212
ERROR_WMI_READ_ONLY = 4213
ERROR_WMI_SET_FAILURE = 4214
ERROR_INVALID_MEDIA = 4300
ERROR_INVALID_LIBRARY = 4301
ERROR_INVALID_MEDIA_POOL = 4302
ERROR_DRIVE_MEDIA_MISMATCH = 4303
ERROR_MEDIA_OFFLINE = 4304
ERROR_LIBRARY_OFFLINE = 4305
ERROR_EMPTY = 4306
ERROR_NOT_EMPTY = 4307
ERROR_MEDIA_UNAVAILABLE = 4308
ERROR_RESOURCE_DISABLED = 4309
ERROR_INVALID_CLEANER = 4310
ERROR_UNABLE_TO_CLEAN = 4311
ERROR_OBJECT_NOT_FOUND = 4312
ERROR_DATABASE_FAILURE = 4313
ERROR_DATABASE_FULL = 4314
ERROR_MEDIA_INCOMPATIBLE = 4315
ERROR_RESOURCE_NOT_PRESENT = 4316
ERROR_INVALID_OPERATION = 4317
ERROR_MEDIA_NOT_AVAILABLE = 4318
ERROR_DEVICE_NOT_AVAILABLE = 4319
ERROR_REQUEST_REFUSED = 4320
ERROR_INVALID_DRIVE_OBJECT = 4321
ERROR_LIBRARY_FULL = 4322
ERROR_MEDIUM_NOT_ACCESSIBLE = 4323
ERROR_UNABLE_TO_LOAD_MEDIUM = 4324
ERROR_UNABLE_TO_INVENTORY_DRIVE = 4325
ERROR_UNABLE_TO_INVENTORY_SLOT = 4326
ERROR_UNABLE_TO_INVENTORY_TRANSPORT = 4327
ERROR_TRANSPORT_FULL = 4328
ERROR_CONTROLLING_IEPORT = 4329
ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA = 4330
ERROR_CLEANER_SLOT_SET = 4331
ERROR_CLEANER_SLOT_NOT_SET = 4332
ERROR_CLEANER_CARTRIDGE_SPENT = 4333
ERROR_UNEXPECTED_OMID = 4334
ERROR_CANT_DELETE_LAST_ITEM = 4335
ERROR_MESSAGE_EXCEEDS_MAX_SIZE = 4336
ERROR_VOLUME_CONTAINS_SYS_FILES = 4337
ERROR_INDIGENOUS_TYPE = 4338
ERROR_NO_SUPPORTING_DRIVES = 4339
ERROR_CLEANER_CARTRIDGE_INSTALLED = 4340
ERROR_IEPORT_FULL = 4341
ERROR_FILE_OFFLINE = 4350
ERROR_REMOTE_STORAGE_NOT_ACTIVE = 4351
ERROR_REMOTE_STORAGE_MEDIA_ERROR = 4352
ERROR_NOT_A_REPARSE_POINT = 4390
ERROR_REPARSE_ATTRIBUTE_CONFLICT = 4391
ERROR_INVALID_REPARSE_DATA = 4392
ERROR_REPARSE_TAG_INVALID = 4393
ERROR_REPARSE_TAG_MISMATCH = 4394
ERROR_VOLUME_NOT_SIS_ENABLED = 4500
ERROR_DEPENDENT_RESOURCE_EXISTS = 5001
ERROR_DEPENDENCY_NOT_FOUND = 5002
ERROR_DEPENDENCY_ALREADY_EXISTS = 5003
ERROR_RESOURCE_NOT_ONLINE = 5004
ERROR_HOST_NODE_NOT_AVAILABLE = 5005
ERROR_RESOURCE_NOT_AVAILABLE = 5006
ERROR_RESOURCE_NOT_FOUND = 5007
ERROR_SHUTDOWN_CLUSTER = 5008
ERROR_CANT_EVICT_ACTIVE_NODE = 5009
ERROR_OBJECT_ALREADY_EXISTS = 5010
ERROR_OBJECT_IN_LIST = 5011
ERROR_GROUP_NOT_AVAILABLE = 5012
ERROR_GROUP_NOT_FOUND = 5013
ERROR_GROUP_NOT_ONLINE = 5014
ERROR_HOST_NODE_NOT_RESOURCE_OWNER = 5015
ERROR_HOST_NODE_NOT_GROUP_OWNER = 5016
ERROR_RESMON_CREATE_FAILED = 5017
ERROR_RESMON_ONLINE_FAILED = 5018
ERROR_RESOURCE_ONLINE = 5019
ERROR_QUORUM_RESOURCE = 5020
ERROR_NOT_QUORUM_CAPABLE = 5021
ERROR_CLUSTER_SHUTTING_DOWN = 5022
ERROR_INVALID_STATE = 5023
ERROR_RESOURCE_PROPERTIES_STORED = 5024
ERROR_NOT_QUORUM_CLASS = 5025
ERROR_CORE_RESOURCE = 5026
ERROR_QUORUM_RESOURCE_ONLINE_FAILED = 5027
ERROR_QUORUMLOG_OPEN_FAILED = 5028
ERROR_CLUSTERLOG_CORRUPT = 5029
ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE = 5030
ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE = 5031
ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND = 5032
ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE = 5033
ERROR_QUORUM_OWNER_ALIVE = 5034
ERROR_NETWORK_NOT_AVAILABLE = 5035
ERROR_NODE_NOT_AVAILABLE = 5036
ERROR_ALL_NODES_NOT_AVAILABLE = 5037
ERROR_RESOURCE_FAILED = 5038
ERROR_CLUSTER_INVALID_NODE = 5039
ERROR_CLUSTER_NODE_EXISTS = 5040
ERROR_CLUSTER_JOIN_IN_PROGRESS = 5041
ERROR_CLUSTER_NODE_NOT_FOUND = 5042
ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND = 5043
ERROR_CLUSTER_NETWORK_EXISTS = 5044
ERROR_CLUSTER_NETWORK_NOT_FOUND = 5045
ERROR_CLUSTER_NETINTERFACE_EXISTS = 5046
ERROR_CLUSTER_NETINTERFACE_NOT_FOUND = 5047
ERROR_CLUSTER_INVALID_REQUEST = 5048
ERROR_CLUSTER_INVALID_NETWORK_PROVIDER = 5049
ERROR_CLUSTER_NODE_DOWN = 5050
ERROR_CLUSTER_NODE_UNREACHABLE = 5051
ERROR_CLUSTER_NODE_NOT_MEMBER = 5052
ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS = 5053
ERROR_CLUSTER_INVALID_NETWORK = 5054
ERROR_CLUSTER_NODE_UP = 5056
ERROR_CLUSTER_IPADDR_IN_USE = 5057
ERROR_CLUSTER_NODE_NOT_PAUSED = 5058
ERROR_CLUSTER_NO_SECURITY_CONTEXT = 5059
ERROR_CLUSTER_NETWORK_NOT_INTERNAL = 5060
ERROR_CLUSTER_NODE_ALREADY_UP = 5061
ERROR_CLUSTER_NODE_ALREADY_DOWN = 5062
ERROR_CLUSTER_NETWORK_ALREADY_ONLINE = 5063
ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE = 5064
ERROR_CLUSTER_NODE_ALREADY_MEMBER = 5065
ERROR_CLUSTER_LAST_INTERNAL_NETWORK = 5066
ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS = 5067
ERROR_INVALID_OPERATION_ON_QUORUM = 5068
ERROR_DEPENDENCY_NOT_ALLOWED = 5069
ERROR_CLUSTER_NODE_PAUSED = 5070
ERROR_NODE_CANT_HOST_RESOURCE = 5071
ERROR_CLUSTER_NODE_NOT_READY = 5072
ERROR_CLUSTER_NODE_SHUTTING_DOWN = 5073
ERROR_CLUSTER_JOIN_ABORTED = 5074
ERROR_CLUSTER_INCOMPATIBLE_VERSIONS = 5075
ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED = 5076
ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED = 5077
ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND = 5078
ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED = 5079
ERROR_CLUSTER_RESNAME_NOT_FOUND = 5080
ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED = 5081
ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST = 5082
ERROR_CLUSTER_DATABASE_SEQMISMATCH = 5083
ERROR_RESMON_INVALID_STATE = 5084
ERROR_CLUSTER_GUM_NOT_LOCKER = 5085
ERROR_QUORUM_DISK_NOT_FOUND = 5086
ERROR_DATABASE_BACKUP_CORRUPT = 5087
ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT = 5088
ERROR_RESOURCE_PROPERTY_UNCHANGEABLE = 5089
ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE = 5890
ERROR_CLUSTER_QUORUMLOG_NOT_FOUND = 5891
ERROR_CLUSTER_MEMBERSHIP_HALT = 5892
ERROR_CLUSTER_INSTANCE_ID_MISMATCH = 5893
ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP = 5894
ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH = 5895
ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP = 5896
ERROR_CLUSTER_PARAMETER_MISMATCH = 5897
ERROR_NODE_CANNOT_BE_CLUSTERED = 5898
ERROR_CLUSTER_WRONG_OS_VERSION = 5899
ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME = 5900
ERROR_CLUSCFG_ALREADY_COMMITTED = 5901
ERROR_CLUSCFG_ROLLBACK_FAILED = 5902
ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT = 5903
ERROR_CLUSTER_OLD_VERSION = 5904
ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME = 5905
ERROR_CLUSTER_NO_NET_ADAPTERS = 5906
ERROR_CLUSTER_POISONED = 5907
ERROR_CLUSTER_GROUP_MOVING = 5908
ERROR_CLUSTER_RESOURCE_TYPE_BUSY = 5909
ERROR_RESOURCE_CALL_TIMED_OUT = 5910
ERROR_INVALID_CLUSTER_IPV6_ADDRESS = 5911
ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION = 5912
ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS = 5913
ERROR_CLUSTER_PARTIAL_SEND = 5914
ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION = 5915
ERROR_CLUSTER_INVALID_STRING_TERMINATION = 5916
ERROR_CLUSTER_INVALID_STRING_FORMAT = 5917
ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS = 5918
ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS = 5919
ERROR_CLUSTER_NULL_DATA = 5920
ERROR_CLUSTER_PARTIAL_READ = 5921
ERROR_CLUSTER_PARTIAL_WRITE = 5922
ERROR_CLUSTER_CANT_DESERIALIZE_DATA = 5923
ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT = 5924
ERROR_CLUSTER_NO_QUORUM = 5925
ERROR_CLUSTER_INVALID_IPV6_NETWORK = 5926
ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK = 5927
ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP = 5928
ERROR_DEPENDENCY_TREE_TOO_COMPLEX = 5929
ERROR_EXCEPTION_IN_RESOURCE_CALL = 5930
ERROR_CLUSTER_RHS_FAILED_INITIALIZATION = 5931
ERROR_CLUSTER_NOT_INSTALLED = 5932
ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE = 5933
ERROR_ENCRYPTION_FAILED = 6000
ERROR_DECRYPTION_FAILED = 6001
ERROR_FILE_ENCRYPTED = 6002
ERROR_NO_RECOVERY_POLICY = 6003
ERROR_NO_EFS = 6004
ERROR_WRONG_EFS = 6005
ERROR_NO_USER_KEYS = 6006
ERROR_FILE_NOT_ENCRYPTED = 6007
ERROR_NOT_EXPORT_FORMAT = 6008
ERROR_FILE_READ_ONLY = 6009
ERROR_DIR_EFS_DISALLOWED = 6010
ERROR_EFS_SERVER_NOT_TRUSTED = 6011
ERROR_BAD_RECOVERY_POLICY = 6012
ERROR_EFS_ALG_BLOB_TOO_BIG = 6013
ERROR_VOLUME_NOT_SUPPORT_EFS = 6014
ERROR_EFS_DISABLED = 6015
ERROR_EFS_VERSION_NOT_SUPPORT = 6016
ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE = 6017
ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER = 6018
ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE = 6019
ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE = 6020
ERROR_CS_ENCRYPTION_FILE_NOT_CSE = 6021
ERROR_NO_BROWSER_SERVERS_FOUND = 6118
ERROR_LOG_SECTOR_INVALID = 6600
ERROR_LOG_SECTOR_PARITY_INVALID = 6601
ERROR_LOG_SECTOR_REMAPPED = 6602
ERROR_LOG_BLOCK_INCOMPLETE = 6603
ERROR_LOG_INVALID_RANGE = 6604
ERROR_LOG_BLOCKS_EXHAUSTED = 6605
ERROR_LOG_READ_CONTEXT_INVALID = 6606
ERROR_LOG_RESTART_INVALID = 6607
ERROR_LOG_BLOCK_VERSION = 6608
ERROR_LOG_BLOCK_INVALID = 6609
ERROR_LOG_READ_MODE_INVALID = 6610
ERROR_LOG_NO_RESTART = 6611
ERROR_LOG_METADATA_CORRUPT = 6612
ERROR_LOG_METADATA_INVALID = 6613
ERROR_LOG_METADATA_INCONSISTENT = 6614
ERROR_LOG_RESERVATION_INVALID = 6615
ERROR_LOG_CANT_DELETE = 6616
ERROR_LOG_CONTAINER_LIMIT_EXCEEDED = 6617
ERROR_LOG_START_OF_LOG = 6618
ERROR_LOG_POLICY_ALREADY_INSTALLED = 6619
ERROR_LOG_POLICY_NOT_INSTALLED = 6620
ERROR_LOG_POLICY_INVALID = 6621
ERROR_LOG_POLICY_CONFLICT = 6622
ERROR_LOG_PINNED_ARCHIVE_TAIL = 6623
ERROR_LOG_RECORD_NONEXISTENT = 6624
ERROR_LOG_RECORDS_RESERVED_INVALID = 6625
ERROR_LOG_SPACE_RESERVED_INVALID = 6626
ERROR_LOG_TAIL_INVALID = 6627
ERROR_LOG_FULL = 6628
ERROR_COULD_NOT_RESIZE_LOG = 6629
ERROR_LOG_MULTIPLEXED = 6630
ERROR_LOG_DEDICATED = 6631
ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS = 6632
ERROR_LOG_ARCHIVE_IN_PROGRESS = 6633
ERROR_LOG_EPHEMERAL = 6634
ERROR_LOG_NOT_ENOUGH_CONTAINERS = 6635
ERROR_LOG_CLIENT_ALREADY_REGISTERED = 6636
ERROR_LOG_CLIENT_NOT_REGISTERED = 6637
ERROR_LOG_FULL_HANDLER_IN_PROGRESS = 6638
ERROR_LOG_CONTAINER_READ_FAILED = 6639
ERROR_LOG_CONTAINER_WRITE_FAILED = 6640
ERROR_LOG_CONTAINER_OPEN_FAILED = 6641
ERROR_LOG_CONTAINER_STATE_INVALID = 6642
ERROR_LOG_STATE_INVALID = 6643
ERROR_LOG_PINNED = 6644
ERROR_LOG_METADATA_FLUSH_FAILED = 6645
ERROR_LOG_INCONSISTENT_SECURITY = 6646
ERROR_LOG_APPENDED_FLUSH_FAILED = 6647
ERROR_LOG_PINNED_RESERVATION = 6648
ERROR_INVALID_TRANSACTION = 6700
ERROR_TRANSACTION_NOT_ACTIVE = 6701
ERROR_TRANSACTION_REQUEST_NOT_VALID = 6702
ERROR_TRANSACTION_NOT_REQUESTED = 6703
ERROR_TRANSACTION_ALREADY_ABORTED = 6704
ERROR_TRANSACTION_ALREADY_COMMITTED = 6705
ERROR_TM_INITIALIZATION_FAILED = 6706
ERROR_RESOURCEMANAGER_READ_ONLY = 6707
ERROR_TRANSACTION_NOT_JOINED = 6708
ERROR_TRANSACTION_SUPERIOR_EXISTS = 6709
ERROR_CRM_PROTOCOL_ALREADY_EXISTS = 6710
ERROR_TRANSACTION_PROPAGATION_FAILED = 6711
ERROR_CRM_PROTOCOL_NOT_FOUND = 6712
ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER = 6713
ERROR_CURRENT_TRANSACTION_NOT_VALID = 6714
ERROR_TRANSACTION_NOT_FOUND = 6715
ERROR_RESOURCEMANAGER_NOT_FOUND = 6716
ERROR_ENLISTMENT_NOT_FOUND = 6717
ERROR_TRANSACTIONMANAGER_NOT_FOUND = 6718
ERROR_TRANSACTIONMANAGER_NOT_ONLINE = 6719
ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION = 6720
ERROR_TRANSACTION_NOT_ROOT = 6721
ERROR_TRANSACTION_OBJECT_EXPIRED = 6722
ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED = 6723
ERROR_TRANSACTION_RECORD_TOO_LONG = 6724
ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED = 6725
ERROR_TRANSACTION_INTEGRITY_VIOLATED = 6726
ERROR_TRANSACTIONAL_CONFLICT = 6800
ERROR_RM_NOT_ACTIVE = 6801
ERROR_RM_METADATA_CORRUPT = 6802
ERROR_DIRECTORY_NOT_RM = 6803
ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE = 6805
ERROR_LOG_RESIZE_INVALID_SIZE = 6806
ERROR_OBJECT_NO_LONGER_EXISTS = 6807
ERROR_STREAM_MINIVERSION_NOT_FOUND = 6808
ERROR_STREAM_MINIVERSION_NOT_VALID = 6809
ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION = 6810
ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT = 6811
ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS = 6812
ERROR_REMOTE_FILE_VERSION_MISMATCH = 6814
ERROR_HANDLE_NO_LONGER_VALID = 6815
ERROR_NO_TXF_METADATA = 6816
ERROR_LOG_CORRUPTION_DETECTED = 6817
ERROR_CANT_RECOVER_WITH_HANDLE_OPEN = 6818
ERROR_RM_DISCONNECTED = 6819
ERROR_ENLISTMENT_NOT_SUPERIOR = 6820
ERROR_RECOVERY_NOT_NEEDED = 6821
ERROR_RM_ALREADY_STARTED = 6822
ERROR_FILE_IDENTITY_NOT_PERSISTENT = 6823
ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY = 6824
ERROR_CANT_CROSS_RM_BOUNDARY = 6825
ERROR_TXF_DIR_NOT_EMPTY = 6826
ERROR_INDOUBT_TRANSACTIONS_EXIST = 6827
ERROR_TM_VOLATILE = 6828
ERROR_ROLLBACK_TIMER_EXPIRED = 6829
ERROR_TXF_ATTRIBUTE_CORRUPT = 6830
ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION = 6831
ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED = 6832
ERROR_LOG_GROWTH_FAILED = 6833
ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE = 6834
ERROR_TXF_METADATA_ALREADY_PRESENT = 6835
ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET = 6836
ERROR_TRANSACTION_REQUIRED_PROMOTION = 6837
ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION = 6838
ERROR_TRANSACTIONS_NOT_FROZEN = 6839
ERROR_TRANSACTION_FREEZE_IN_PROGRESS = 6840
ERROR_NOT_SNAPSHOT_VOLUME = 6841
ERROR_NO_SAVEPOINT_WITH_OPEN_FILES = 6842
ERROR_DATA_LOST_REPAIR = 6843
ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION = 6844
ERROR_TM_IDENTITY_MISMATCH = 6845
ERROR_FLOATED_SECTION = 6846
ERROR_CANNOT_ACCEPT_TRANSACTED_WORK = 6847
ERROR_CANNOT_ABORT_TRANSACTIONS = 6848
ERROR_BAD_CLUSTERS = 6849
ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION = 6850
ERROR_VOLUME_DIRTY = 6851
ERROR_NO_LINK_TRACKING_IN_TRANSACTION = 6852
ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION = 6853
ERROR_CTX_WINSTATION_NAME_INVALID = 7001
ERROR_CTX_INVALID_PD = 7002
ERROR_CTX_PD_NOT_FOUND = 7003
ERROR_CTX_WD_NOT_FOUND = 7004
ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY = 7005
ERROR_CTX_SERVICE_NAME_COLLISION = 7006
ERROR_CTX_CLOSE_PENDING = 7007
ERROR_CTX_NO_OUTBUF = 7008
ERROR_CTX_MODEM_INF_NOT_FOUND = 7009
ERROR_CTX_INVALID_MODEMNAME = 7010
ERROR_CTX_MODEM_RESPONSE_ERROR = 7011
ERROR_CTX_MODEM_RESPONSE_TIMEOUT = 7012
ERROR_CTX_MODEM_RESPONSE_NO_CARRIER = 7013
ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE = 7014
ERROR_CTX_MODEM_RESPONSE_BUSY = 7015
ERROR_CTX_MODEM_RESPONSE_VOICE = 7016
ERROR_CTX_TD_ERROR = 7017
ERROR_CTX_WINSTATION_NOT_FOUND = 7022
ERROR_CTX_WINSTATION_ALREADY_EXISTS = 7023
ERROR_CTX_WINSTATION_BUSY = 7024
ERROR_CTX_BAD_VIDEO_MODE = 7025
ERROR_CTX_GRAPHICS_INVALID = 7035
ERROR_CTX_LOGON_DISABLED = 7037
ERROR_CTX_NOT_CONSOLE = 7038
ERROR_CTX_CLIENT_QUERY_TIMEOUT = 7040
ERROR_CTX_CONSOLE_DISCONNECT = 7041
ERROR_CTX_CONSOLE_CONNECT = 7042
ERROR_CTX_SHADOW_DENIED = 7044
ERROR_CTX_WINSTATION_ACCESS_DENIED = 7045
ERROR_CTX_INVALID_WD = 7049
ERROR_CTX_SHADOW_INVALID = 7050
ERROR_CTX_SHADOW_DISABLED = 7051
ERROR_CTX_CLIENT_LICENSE_IN_USE = 7052
ERROR_CTX_CLIENT_LICENSE_NOT_SET = 7053
ERROR_CTX_LICENSE_NOT_AVAILABLE = 7054
ERROR_CTX_LICENSE_CLIENT_INVALID = 7055
ERROR_CTX_LICENSE_EXPIRED = 7056
ERROR_CTX_SHADOW_NOT_RUNNING = 7057
ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE = 7058
ERROR_ACTIVATION_COUNT_EXCEEDED = 7059
ERROR_CTX_WINSTATIONS_DISABLED = 7060
ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED = 7061
ERROR_CTX_SESSION_IN_USE = 7062
ERROR_CTX_NO_FORCE_LOGOFF = 7063
ERROR_CTX_ACCOUNT_RESTRICTION = 7064
ERROR_RDP_PROTOCOL_ERROR = 7065
ERROR_CTX_CDM_CONNECT = 7066
ERROR_CTX_CDM_DISCONNECT = 7067
ERROR_CTX_SECURITY_LAYER_ERROR = 7068
ERROR_TS_INCOMPATIBLE_SESSIONS = 7069
FRS_ERR_INVALID_API_SEQUENCE = 8001
FRS_ERR_STARTING_SERVICE = 8002
FRS_ERR_STOPPING_SERVICE = 8003
FRS_ERR_INTERNAL_API = 8004
FRS_ERR_INTERNAL = 8005
FRS_ERR_SERVICE_COMM = 8006
FRS_ERR_INSUFFICIENT_PRIV = 8007
FRS_ERR_AUTHENTICATION = 8008
FRS_ERR_PARENT_INSUFFICIENT_PRIV = 8009
FRS_ERR_PARENT_AUTHENTICATION = 8010
FRS_ERR_CHILD_TO_PARENT_COMM = 8011
FRS_ERR_PARENT_TO_CHILD_COMM = 8012
FRS_ERR_SYSVOL_POPULATE = 8013
FRS_ERR_SYSVOL_POPULATE_TIMEOUT = 8014
FRS_ERR_SYSVOL_IS_BUSY = 8015
FRS_ERR_SYSVOL_DEMOTE = 8016
FRS_ERR_INVALID_SERVICE_PARAMETER = 8017
DS_S_SUCCESS = NO_ERROR
ERROR_DS_NOT_INSTALLED = 8200
ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY = 8201
ERROR_DS_NO_ATTRIBUTE_OR_VALUE = 8202
ERROR_DS_INVALID_ATTRIBUTE_SYNTAX = 8203
ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED = 8204
ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS = 8205
ERROR_DS_BUSY = 8206
ERROR_DS_UNAVAILABLE = 8207
ERROR_DS_NO_RIDS_ALLOCATED = 8208
ERROR_DS_NO_MORE_RIDS = 8209
ERROR_DS_INCORRECT_ROLE_OWNER = 8210
ERROR_DS_RIDMGR_INIT_ERROR = 8211
ERROR_DS_OBJ_CLASS_VIOLATION = 8212
ERROR_DS_CANT_ON_NON_LEAF = 8213
ERROR_DS_CANT_ON_RDN = 8214
ERROR_DS_CANT_MOD_OBJ_CLASS = 8215
ERROR_DS_CROSS_DOM_MOVE_ERROR = 8216
ERROR_DS_GC_NOT_AVAILABLE = 8217
ERROR_SHARED_POLICY = 8218
ERROR_POLICY_OBJECT_NOT_FOUND = 8219
ERROR_POLICY_ONLY_IN_DS = 8220
ERROR_PROMOTION_ACTIVE = 8221
ERROR_NO_PROMOTION_ACTIVE = 8222
ERROR_DS_OPERATIONS_ERROR = 8224
ERROR_DS_PROTOCOL_ERROR = 8225
ERROR_DS_TIMELIMIT_EXCEEDED = 8226
ERROR_DS_SIZELIMIT_EXCEEDED = 8227
ERROR_DS_ADMIN_LIMIT_EXCEEDED = 8228
ERROR_DS_COMPARE_FALSE = 8229
ERROR_DS_COMPARE_TRUE = 8230
ERROR_DS_AUTH_METHOD_NOT_SUPPORTED = 8231
ERROR_DS_STRONG_AUTH_REQUIRED = 8232
ERROR_DS_INAPPROPRIATE_AUTH = 8233
ERROR_DS_AUTH_UNKNOWN = 8234
ERROR_DS_REFERRAL = 8235
ERROR_DS_UNAVAILABLE_CRIT_EXTENSION = 8236
ERROR_DS_CONFIDENTIALITY_REQUIRED = 8237
ERROR_DS_INAPPROPRIATE_MATCHING = 8238
ERROR_DS_CONSTRAINT_VIOLATION = 8239
ERROR_DS_NO_SUCH_OBJECT = 8240
ERROR_DS_ALIAS_PROBLEM = 8241
ERROR_DS_INVALID_DN_SYNTAX = 8242
ERROR_DS_IS_LEAF = 8243
ERROR_DS_ALIAS_DEREF_PROBLEM = 8244
ERROR_DS_UNWILLING_TO_PERFORM = 8245
ERROR_DS_LOOP_DETECT = 8246
ERROR_DS_NAMING_VIOLATION = 8247
ERROR_DS_OBJECT_RESULTS_TOO_LARGE = 8248
ERROR_DS_AFFECTS_MULTIPLE_DSAS = 8249
ERROR_DS_SERVER_DOWN = 8250
ERROR_DS_LOCAL_ERROR = 8251
ERROR_DS_ENCODING_ERROR = 8252
ERROR_DS_DECODING_ERROR = 8253
ERROR_DS_FILTER_UNKNOWN = 8254
ERROR_DS_PARAM_ERROR = 8255
ERROR_DS_NOT_SUPPORTED = 8256
ERROR_DS_NO_RESULTS_RETURNED = 8257
ERROR_DS_CONTROL_NOT_FOUND = 8258
ERROR_DS_CLIENT_LOOP = 8259
ERROR_DS_REFERRAL_LIMIT_EXCEEDED = 8260
ERROR_DS_SORT_CONTROL_MISSING = 8261
ERROR_DS_OFFSET_RANGE_ERROR = 8262
ERROR_DS_ROOT_MUST_BE_NC = 8301
ERROR_DS_ADD_REPLICA_INHIBITED = 8302
ERROR_DS_ATT_NOT_DEF_IN_SCHEMA = 8303
ERROR_DS_MAX_OBJ_SIZE_EXCEEDED = 8304
ERROR_DS_OBJ_STRING_NAME_EXISTS = 8305
ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA = 8306
ERROR_DS_RDN_DOESNT_MATCH_SCHEMA = 8307
ERROR_DS_NO_REQUESTED_ATTS_FOUND = 8308
ERROR_DS_USER_BUFFER_TO_SMALL = 8309
ERROR_DS_ATT_IS_NOT_ON_OBJ = 8310
ERROR_DS_ILLEGAL_MOD_OPERATION = 8311
ERROR_DS_OBJ_TOO_LARGE = 8312
ERROR_DS_BAD_INSTANCE_TYPE = 8313
ERROR_DS_MASTERDSA_REQUIRED = 8314
ERROR_DS_OBJECT_CLASS_REQUIRED = 8315
ERROR_DS_MISSING_REQUIRED_ATT = 8316
ERROR_DS_ATT_NOT_DEF_FOR_CLASS = 8317
ERROR_DS_ATT_ALREADY_EXISTS = 8318
ERROR_DS_CANT_ADD_ATT_VALUES = 8320
ERROR_DS_SINGLE_VALUE_CONSTRAINT = 8321
ERROR_DS_RANGE_CONSTRAINT = 8322
ERROR_DS_ATT_VAL_ALREADY_EXISTS = 8323
ERROR_DS_CANT_REM_MISSING_ATT = 8324
ERROR_DS_CANT_REM_MISSING_ATT_VAL = 8325
ERROR_DS_ROOT_CANT_BE_SUBREF = 8326
ERROR_DS_NO_CHAINING = 8327
ERROR_DS_NO_CHAINED_EVAL = 8328
ERROR_DS_NO_PARENT_OBJECT = 8329
ERROR_DS_PARENT_IS_AN_ALIAS = 8330
ERROR_DS_CANT_MIX_MASTER_AND_REPS = 8331
ERROR_DS_CHILDREN_EXIST = 8332
ERROR_DS_OBJ_NOT_FOUND = 8333
ERROR_DS_ALIASED_OBJ_MISSING = 8334
ERROR_DS_BAD_NAME_SYNTAX = 8335
ERROR_DS_ALIAS_POINTS_TO_ALIAS = 8336
ERROR_DS_CANT_DEREF_ALIAS = 8337
ERROR_DS_OUT_OF_SCOPE = 8338
ERROR_DS_OBJECT_BEING_REMOVED = 8339
ERROR_DS_CANT_DELETE_DSA_OBJ = 8340
ERROR_DS_GENERIC_ERROR = 8341
ERROR_DS_DSA_MUST_BE_INT_MASTER = 8342
ERROR_DS_CLASS_NOT_DSA = 8343
ERROR_DS_INSUFF_ACCESS_RIGHTS = 8344
ERROR_DS_ILLEGAL_SUPERIOR = 8345
ERROR_DS_ATTRIBUTE_OWNED_BY_SAM = 8346
ERROR_DS_NAME_TOO_MANY_PARTS = 8347
ERROR_DS_NAME_TOO_LONG = 8348
ERROR_DS_NAME_VALUE_TOO_LONG = 8349
ERROR_DS_NAME_UNPARSEABLE = 8350
ERROR_DS_NAME_TYPE_UNKNOWN = 8351
ERROR_DS_NOT_AN_OBJECT = 8352
ERROR_DS_SEC_DESC_TOO_SHORT = 8353
ERROR_DS_SEC_DESC_INVALID = 8354
ERROR_DS_NO_DELETED_NAME = 8355
ERROR_DS_SUBREF_MUST_HAVE_PARENT = 8356
ERROR_DS_NCNAME_MUST_BE_NC = 8357
ERROR_DS_CANT_ADD_SYSTEM_ONLY = 8358
ERROR_DS_CLASS_MUST_BE_CONCRETE = 8359
ERROR_DS_INVALID_DMD = 8360
ERROR_DS_OBJ_GUID_EXISTS = 8361
ERROR_DS_NOT_ON_BACKLINK = 8362
ERROR_DS_NO_CROSSREF_FOR_NC = 8363
ERROR_DS_SHUTTING_DOWN = 8364
ERROR_DS_UNKNOWN_OPERATION = 8365
ERROR_DS_INVALID_ROLE_OWNER = 8366
ERROR_DS_COULDNT_CONTACT_FSMO = 8367
ERROR_DS_CROSS_NC_DN_RENAME = 8368
ERROR_DS_CANT_MOD_SYSTEM_ONLY = 8369
ERROR_DS_REPLICATOR_ONLY = 8370
ERROR_DS_OBJ_CLASS_NOT_DEFINED = 8371
ERROR_DS_OBJ_CLASS_NOT_SUBCLASS = 8372
ERROR_DS_NAME_REFERENCE_INVALID = 8373
ERROR_DS_CROSS_REF_EXISTS = 8374
ERROR_DS_CANT_DEL_MASTER_CROSSREF = 8375
ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD = 8376
ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX = 8377
ERROR_DS_DUP_RDN = 8378
ERROR_DS_DUP_OID = 8379
ERROR_DS_DUP_MAPI_ID = 8380
ERROR_DS_DUP_SCHEMA_ID_GUID = 8381
ERROR_DS_DUP_LDAP_DISPLAY_NAME = 8382
ERROR_DS_SEMANTIC_ATT_TEST = 8383
ERROR_DS_SYNTAX_MISMATCH = 8384
ERROR_DS_EXISTS_IN_MUST_HAVE = 8385
ERROR_DS_EXISTS_IN_MAY_HAVE = 8386
ERROR_DS_NONEXISTENT_MAY_HAVE = 8387
ERROR_DS_NONEXISTENT_MUST_HAVE = 8388
ERROR_DS_AUX_CLS_TEST_FAIL = 8389
ERROR_DS_NONEXISTENT_POSS_SUP = 8390
ERROR_DS_SUB_CLS_TEST_FAIL = 8391
ERROR_DS_BAD_RDN_ATT_ID_SYNTAX = 8392
ERROR_DS_EXISTS_IN_AUX_CLS = 8393
ERROR_DS_EXISTS_IN_SUB_CLS = 8394
ERROR_DS_EXISTS_IN_POSS_SUP = 8395
ERROR_DS_RECALCSCHEMA_FAILED = 8396
ERROR_DS_TREE_DELETE_NOT_FINISHED = 8397
ERROR_DS_CANT_DELETE = 8398
ERROR_DS_ATT_SCHEMA_REQ_ID = 8399
ERROR_DS_BAD_ATT_SCHEMA_SYNTAX = 8400
ERROR_DS_CANT_CACHE_ATT = 8401
ERROR_DS_CANT_CACHE_CLASS = 8402
ERROR_DS_CANT_REMOVE_ATT_CACHE = 8403
ERROR_DS_CANT_REMOVE_CLASS_CACHE = 8404
ERROR_DS_CANT_RETRIEVE_DN = 8405
ERROR_DS_MISSING_SUPREF = 8406
ERROR_DS_CANT_RETRIEVE_INSTANCE = 8407
ERROR_DS_CODE_INCONSISTENCY = 8408
ERROR_DS_DATABASE_ERROR = 8409
ERROR_DS_GOVERNSID_MISSING = 8410
ERROR_DS_MISSING_EXPECTED_ATT = 8411
ERROR_DS_NCNAME_MISSING_CR_REF = 8412
ERROR_DS_SECURITY_CHECKING_ERROR = 8413
ERROR_DS_SCHEMA_NOT_LOADED = 8414
ERROR_DS_SCHEMA_ALLOC_FAILED = 8415
ERROR_DS_ATT_SCHEMA_REQ_SYNTAX = 8416
ERROR_DS_GCVERIFY_ERROR = 8417
ERROR_DS_DRA_SCHEMA_MISMATCH = 8418
ERROR_DS_CANT_FIND_DSA_OBJ = 8419
ERROR_DS_CANT_FIND_EXPECTED_NC = 8420
ERROR_DS_CANT_FIND_NC_IN_CACHE = 8421
ERROR_DS_CANT_RETRIEVE_CHILD = 8422
ERROR_DS_SECURITY_ILLEGAL_MODIFY = 8423
ERROR_DS_CANT_REPLACE_HIDDEN_REC = 8424
ERROR_DS_BAD_HIERARCHY_FILE = 8425
ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED = 8426
ERROR_DS_CONFIG_PARAM_MISSING = 8427
ERROR_DS_COUNTING_AB_INDICES_FAILED = 8428
ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED = 8429
ERROR_DS_INTERNAL_FAILURE = 8430
ERROR_DS_UNKNOWN_ERROR = 8431
ERROR_DS_ROOT_REQUIRES_CLASS_TOP = 8432
ERROR_DS_REFUSING_FSMO_ROLES = 8433
ERROR_DS_MISSING_FSMO_SETTINGS = 8434
ERROR_DS_UNABLE_TO_SURRENDER_ROLES = 8435
ERROR_DS_DRA_GENERIC = 8436
ERROR_DS_DRA_INVALID_PARAMETER = 8437
ERROR_DS_DRA_BUSY = 8438
ERROR_DS_DRA_BAD_DN = 8439
ERROR_DS_DRA_BAD_NC = 8440
ERROR_DS_DRA_DN_EXISTS = 8441
ERROR_DS_DRA_INTERNAL_ERROR = 8442
ERROR_DS_DRA_INCONSISTENT_DIT = 8443
ERROR_DS_DRA_CONNECTION_FAILED = 8444
ERROR_DS_DRA_BAD_INSTANCE_TYPE = 8445
ERROR_DS_DRA_OUT_OF_MEM = 8446
ERROR_DS_DRA_MAIL_PROBLEM = 8447
ERROR_DS_DRA_REF_ALREADY_EXISTS = 8448
ERROR_DS_DRA_REF_NOT_FOUND = 8449
ERROR_DS_DRA_OBJ_IS_REP_SOURCE = 8450
ERROR_DS_DRA_DB_ERROR = 8451
ERROR_DS_DRA_NO_REPLICA = 8452
ERROR_DS_DRA_ACCESS_DENIED = 8453
ERROR_DS_DRA_NOT_SUPPORTED = 8454
ERROR_DS_DRA_RPC_CANCELLED = 8455
ERROR_DS_DRA_SOURCE_DISABLED = 8456
ERROR_DS_DRA_SINK_DISABLED = 8457
ERROR_DS_DRA_NAME_COLLISION = 8458
ERROR_DS_DRA_SOURCE_REINSTALLED = 8459
ERROR_DS_DRA_MISSING_PARENT = 8460
ERROR_DS_DRA_PREEMPTED = 8461
ERROR_DS_DRA_ABANDON_SYNC = 8462
ERROR_DS_DRA_SHUTDOWN = 8463
ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET = 8464
ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA = 8465
ERROR_DS_DRA_EXTN_CONNECTION_FAILED = 8466
ERROR_DS_INSTALL_SCHEMA_MISMATCH = 8467
ERROR_DS_DUP_LINK_ID = 8468
ERROR_DS_NAME_ERROR_RESOLVING = 8469
ERROR_DS_NAME_ERROR_NOT_FOUND = 8470
ERROR_DS_NAME_ERROR_NOT_UNIQUE = 8471
ERROR_DS_NAME_ERROR_NO_MAPPING = 8472
ERROR_DS_NAME_ERROR_DOMAIN_ONLY = 8473
ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING = 8474
ERROR_DS_CONSTRUCTED_ATT_MOD = 8475
ERROR_DS_WRONG_OM_OBJ_CLASS = 8476
ERROR_DS_DRA_REPL_PENDING = 8477
ERROR_DS_DS_REQUIRED = 8478
ERROR_DS_INVALID_LDAP_DISPLAY_NAME = 8479
ERROR_DS_NON_BASE_SEARCH = 8480
ERROR_DS_CANT_RETRIEVE_ATTS = 8481
ERROR_DS_BACKLINK_WITHOUT_LINK = 8482
ERROR_DS_EPOCH_MISMATCH = 8483
ERROR_DS_SRC_NAME_MISMATCH = 8484
ERROR_DS_SRC_AND_DST_NC_IDENTICAL = 8485
ERROR_DS_DST_NC_MISMATCH = 8486
ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC = 8487
ERROR_DS_SRC_GUID_MISMATCH = 8488
ERROR_DS_CANT_MOVE_DELETED_OBJECT = 8489
ERROR_DS_PDC_OPERATION_IN_PROGRESS = 8490
ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD = 8491
ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION = 8492
ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS = 8493
ERROR_DS_NC_MUST_HAVE_NC_PARENT = 8494
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE = 8495
ERROR_DS_DST_DOMAIN_NOT_NATIVE = 8496
ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER = 8497
ERROR_DS_CANT_MOVE_ACCOUNT_GROUP = 8498
ERROR_DS_CANT_MOVE_RESOURCE_GROUP = 8499
ERROR_DS_INVALID_SEARCH_FLAG = 8500
ERROR_DS_NO_TREE_DELETE_ABOVE_NC = 8501
ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE = 8502
ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE = 8503
ERROR_DS_SAM_INIT_FAILURE = 8504
ERROR_DS_SENSITIVE_GROUP_VIOLATION = 8505
ERROR_DS_CANT_MOD_PRIMARYGROUPID = 8506
ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD = 8507
ERROR_DS_NONSAFE_SCHEMA_CHANGE = 8508
ERROR_DS_SCHEMA_UPDATE_DISALLOWED = 8509
ERROR_DS_CANT_CREATE_UNDER_SCHEMA = 8510
ERROR_DS_INSTALL_NO_SRC_SCH_VERSION = 8511
ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE = 8512
ERROR_DS_INVALID_GROUP_TYPE = 8513
ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN = 8514
ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN = 8515
ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER = 8516
ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER = 8517
ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER = 8518
ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER = 8519
ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER = 8520
ERROR_DS_HAVE_PRIMARY_MEMBERS = 8521
ERROR_DS_STRING_SD_CONVERSION_FAILED = 8522
ERROR_DS_NAMING_MASTER_GC = 8523
ERROR_DS_DNS_LOOKUP_FAILURE = 8524
ERROR_DS_COULDNT_UPDATE_SPNS = 8525
ERROR_DS_CANT_RETRIEVE_SD = 8526
ERROR_DS_KEY_NOT_UNIQUE = 8527
ERROR_DS_WRONG_LINKED_ATT_SYNTAX = 8528
ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD = 8529
ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY = 8530
ERROR_DS_CANT_START = 8531
ERROR_DS_INIT_FAILURE = 8532
ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION = 8533
ERROR_DS_SOURCE_DOMAIN_IN_FOREST = 8534
ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST = 8535
ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED = 8536
ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN = 8537
ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER = 8538
ERROR_DS_SRC_SID_EXISTS_IN_FOREST = 8539
ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH = 8540
ERROR_SAM_INIT_FAILURE = 8541
ERROR_DS_DRA_SCHEMA_INFO_SHIP = 8542
ERROR_DS_DRA_SCHEMA_CONFLICT = 8543
ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT = 8544
ERROR_DS_DRA_OBJ_NC_MISMATCH = 8545
ERROR_DS_NC_STILL_HAS_DSAS = 8546
ERROR_DS_GC_REQUIRED = 8547
ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY = 8548
ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS = 8549
ERROR_DS_CANT_ADD_TO_GC = 8550
ERROR_DS_NO_CHECKPOINT_WITH_PDC = 8551
ERROR_DS_SOURCE_AUDITING_NOT_ENABLED = 8552
ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC = 8553
ERROR_DS_INVALID_NAME_FOR_SPN = 8554
ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS = 8555
ERROR_DS_UNICODEPWD_NOT_IN_QUOTES = 8556
ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED = 8557
ERROR_DS_MUST_BE_RUN_ON_DST_DC = 8558
ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER = 8559
ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ = 8560
ERROR_DS_INIT_FAILURE_CONSOLE = 8561
ERROR_DS_SAM_INIT_FAILURE_CONSOLE = 8562
ERROR_DS_FOREST_VERSION_TOO_HIGH = 8563
ERROR_DS_DOMAIN_VERSION_TOO_HIGH = 8564
ERROR_DS_FOREST_VERSION_TOO_LOW = 8565
ERROR_DS_DOMAIN_VERSION_TOO_LOW = 8566
ERROR_DS_INCOMPATIBLE_VERSION = 8567
ERROR_DS_LOW_DSA_VERSION = 8568
ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN = 8569
ERROR_DS_NOT_SUPPORTED_SORT_ORDER = 8570
ERROR_DS_NAME_NOT_UNIQUE = 8571
ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 = 8572
ERROR_DS_OUT_OF_VERSION_STORE = 8573
ERROR_DS_INCOMPATIBLE_CONTROLS_USED = 8574
ERROR_DS_NO_REF_DOMAIN = 8575
ERROR_DS_RESERVED_LINK_ID = 8576
ERROR_DS_LINK_ID_NOT_AVAILABLE = 8577
ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER = 8578
ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE = 8579
ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC = 8580
ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG = 8581
ERROR_DS_MODIFYDN_WRONG_GRANDPARENT = 8582
ERROR_DS_NAME_ERROR_TRUST_REFERRAL = 8583
ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER = 8584
ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD = 8585
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 = 8586
ERROR_DS_THREAD_LIMIT_EXCEEDED = 8587
ERROR_DS_NOT_CLOSEST = 8588
ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF = 8589
ERROR_DS_SINGLE_USER_MODE_FAILED = 8590
ERROR_DS_NTDSCRIPT_SYNTAX_ERROR = 8591
ERROR_DS_NTDSCRIPT_PROCESS_ERROR = 8592
ERROR_DS_DIFFERENT_REPL_EPOCHS = 8593
ERROR_DS_DRS_EXTENSIONS_CHANGED = 8594
ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR = 8595
ERROR_DS_NO_MSDS_INTID = 8596
ERROR_DS_DUP_MSDS_INTID = 8597
ERROR_DS_EXISTS_IN_RDNATTID = 8598
ERROR_DS_AUTHORIZATION_FAILED = 8599
ERROR_DS_INVALID_SCRIPT = 8600
ERROR_DS_REMOTE_CROSSREF_OP_FAILED = 8601
ERROR_DS_CROSS_REF_BUSY = 8602
ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN = 8603
ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC = 8604
ERROR_DS_DUPLICATE_ID_FOUND = 8605
ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT = 8606
ERROR_DS_GROUP_CONVERSION_ERROR = 8607
ERROR_DS_CANT_MOVE_APP_BASIC_GROUP = 8608
ERROR_DS_CANT_MOVE_APP_QUERY_GROUP = 8609
ERROR_DS_ROLE_NOT_VERIFIED = 8610
ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL = 8611
ERROR_DS_DOMAIN_RENAME_IN_PROGRESS = 8612
ERROR_DS_EXISTING_AD_CHILD_NC = 8613
ERROR_DS_REPL_LIFETIME_EXCEEDED = 8614
ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER = 8615
ERROR_DS_LDAP_SEND_QUEUE_FULL = 8616
ERROR_DS_DRA_OUT_SCHEDULE_WINDOW = 8617
ERROR_DS_POLICY_NOT_KNOWN = 8618
ERROR_NO_SITE_SETTINGS_OBJECT = 8619
ERROR_NO_SECRETS = 8620
ERROR_NO_WRITABLE_DC_FOUND = 8621
ERROR_DS_NO_SERVER_OBJECT = 8622
ERROR_DS_NO_NTDSA_OBJECT = 8623
ERROR_DS_NON_ASQ_SEARCH = 8624
ERROR_DS_AUDIT_FAILURE = 8625
ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE = 8626
ERROR_DS_INVALID_SEARCH_FLAG_TUPLE = 8627
ERROR_DS_HIERARCHY_TABLE_TOO_DEEP = 8628

SEVERITY_SUCCESS = 0
SEVERITY_ERROR = 1


def HRESULT_FROM_WIN32(scode):
    return -2147024896 | (scode & 65535)


def SUCCEEDED(Status):
    return (Status) >= 0


def FAILED(Status):
    return Status < 0


def HRESULT_CODE(hr):
    return (hr) & 65535


def SCODE_CODE(sc):
    return (sc) & 65535


def HRESULT_FACILITY(hr):
    return ((hr) >> 16) & 8191


def SCODE_FACILITY(sc):
    return ((sc) >> 16) & 8191


def HRESULT_SEVERITY(hr):
    return ((hr) >> 31) & 1


def SCODE_SEVERITY(sc):
    return ((sc) >> 31) & 1


FACILITY_NT_BIT = 268435456


def HRESULT_FROM_NT(x):
    return x | FACILITY_NT_BIT


def GetScode(hr):
    return hr


def ResultFromScode(sc):
    return sc


NOERROR = 0
E_UNEXPECTED = -2147418113
E_NOTIMPL = -2147467263
E_OUTOFMEMORY = -2147024882
E_INVALIDARG = -2147024809
E_NOINTERFACE = -2147467262
E_POINTER = -2147467261
E_HANDLE = -2147024890
E_ABORT = -2147467260
E_FAIL = -2147467259
E_ACCESSDENIED = -2147024891
win16_E_NOTIMPL = -2147483647
win16_E_OUTOFMEMORY = -2147483646
win16_E_INVALIDARG = -2147483645
win16_E_NOINTERFACE = -2147483644
win16_E_POINTER = -2147483643
win16_E_HANDLE = -2147483642
win16_E_ABORT = -2147483641
win16_E_FAIL = -2147483640
win16_E_ACCESSDENIED = -2147483639
E_PENDING = -2147483638
CO_E_INIT_TLS = -2147467258
CO_E_INIT_SHARED_ALLOCATOR = -2147467257
CO_E_INIT_MEMORY_ALLOCATOR = -2147467256
CO_E_INIT_CLASS_CACHE = -2147467255
CO_E_INIT_RPC_CHANNEL = -2147467254
CO_E_INIT_TLS_SET_CHANNEL_CONTROL = -2147467253
CO_E_INIT_TLS_CHANNEL_CONTROL = -2147467252
CO_E_INIT_UNACCEPTED_USER_ALLOCATOR = -2147467251
CO_E_INIT_SCM_MUTEX_EXISTS = -2147467250
CO_E_INIT_SCM_FILE_MAPPING_EXISTS = -2147467249
CO_E_INIT_SCM_MAP_VIEW_OF_FILE = -2147467248
CO_E_INIT_SCM_EXEC_FAILURE = -2147467247
CO_E_INIT_ONLY_SINGLE_THREADED = -2147467246
CO_E_CANT_REMOTE = -2147467245
CO_E_BAD_SERVER_NAME = -2147467244
CO_E_WRONG_SERVER_IDENTITY = -2147467243
CO_E_OLE1DDE_DISABLED = -2147467242
CO_E_RUNAS_SYNTAX = -2147467241
CO_E_CREATEPROCESS_FAILURE = -2147467240
CO_E_RUNAS_CREATEPROCESS_FAILURE = -2147467239
CO_E_RUNAS_LOGON_FAILURE = -2147467238
CO_E_LAUNCH_PERMSSION_DENIED = -2147467237
CO_E_START_SERVICE_FAILURE = -2147467236
CO_E_REMOTE_COMMUNICATION_FAILURE = -2147467235
CO_E_SERVER_START_TIMEOUT = -2147467234
CO_E_CLSREG_INCONSISTENT = -2147467233
CO_E_IIDREG_INCONSISTENT = -2147467232
CO_E_NOT_SUPPORTED = -2147467231
CO_E_RELOAD_DLL = -2147467230
CO_E_MSI_ERROR = -2147467229

OLE_E_FIRST = -2147221504
OLE_E_LAST = -2147221249
OLE_S_FIRST = 262144
OLE_S_LAST = 262399

OLE_E_OLEVERB = -2147221504
OLE_E_ADVF = -2147221503
OLE_E_ENUM_NOMORE = -2147221502
OLE_E_ADVISENOTSUPPORTED = -2147221501
OLE_E_NOCONNECTION = -2147221500
OLE_E_NOTRUNNING = -2147221499
OLE_E_NOCACHE = -2147221498
OLE_E_BLANK = -2147221497
OLE_E_CLASSDIFF = -2147221496
OLE_E_CANT_GETMONIKER = -2147221495
OLE_E_CANT_BINDTOSOURCE = -2147221494
OLE_E_STATIC = -2147221493
OLE_E_PROMPTSAVECANCELLED = -2147221492
OLE_E_INVALIDRECT = -2147221491
OLE_E_WRONGCOMPOBJ = -2147221490
OLE_E_INVALIDHWND = -2147221489
OLE_E_NOT_INPLACEACTIVE = -2147221488
OLE_E_CANTCONVERT = -2147221487
OLE_E_NOSTORAGE = -2147221486
DV_E_FORMATETC = -2147221404
DV_E_DVTARGETDEVICE = -2147221403
DV_E_STGMEDIUM = -2147221402
DV_E_STATDATA = -2147221401
DV_E_LINDEX = -2147221400
DV_E_TYMED = -2147221399
DV_E_CLIPFORMAT = -2147221398
DV_E_DVASPECT = -2147221397
DV_E_DVTARGETDEVICE_SIZE = -**********
DV_E_NOIVIEWOBJECT = -**********
DRAGDROP_E_FIRST = -**********
DRAGDROP_E_LAST = -**********
DRAGDROP_S_FIRST = 262400
DRAGDROP_S_LAST = 262415
DRAGDROP_E_NOTREGISTERED = -**********
DRAGDROP_E_ALREADYREGISTERED = -**********
DRAGDROP_E_INVALIDHWND = -**********
CLASSFACTORY_E_FIRST = -**********
CLASSFACTORY_E_LAST = -**********
CLASSFACTORY_S_FIRST = 262416
CLASSFACTORY_S_LAST = 262431
CLASS_E_NOAGGREGATION = -**********
CLASS_E_CLASSNOTAVAILABLE = -**********
CLASS_E_NOTLICENSED = -**********
MARSHAL_E_FIRST = -**********
MARSHAL_E_LAST = -**********
MARSHAL_S_FIRST = 262432
MARSHAL_S_LAST = 262447
DATA_E_FIRST = -**********
DATA_E_LAST = -**********
DATA_S_FIRST = 262448
DATA_S_LAST = 262463
VIEW_E_FIRST = -**********
VIEW_E_LAST = -**********
VIEW_S_FIRST = 262464
VIEW_S_LAST = 262479
VIEW_E_DRAW = -**********
REGDB_E_FIRST = -**********
REGDB_E_LAST = -**********
REGDB_S_FIRST = 262480
REGDB_S_LAST = 262495
REGDB_E_READREGDB = -**********
REGDB_E_WRITEREGDB = -**********
REGDB_E_KEYMISSING = -**********
REGDB_E_INVALIDVALUE = -**********
REGDB_E_CLASSNOTREG = -**********
REGDB_E_IIDNOTREG = -**********
CAT_E_FIRST = -**********
CAT_E_LAST = -**********
CAT_E_CATIDNOEXIST = -**********
CAT_E_NODESCRIPTION = -**********
CS_E_FIRST = -**********
CS_E_LAST = -**********
CS_E_PACKAGE_NOTFOUND = -**********
CS_E_NOT_DELETABLE = -**********
CS_E_CLASS_NOTFOUND = -**********
CS_E_INVALID_VERSION = -**********
CS_E_NO_CLASSSTORE = -**********
CACHE_E_FIRST = -2147221136
CACHE_E_LAST = -2147221121
CACHE_S_FIRST = 262512
CACHE_S_LAST = 262527
CACHE_E_NOCACHE_UPDATED = -2147221136
OLEOBJ_E_FIRST = -2147221120
OLEOBJ_E_LAST = -2147221105
OLEOBJ_S_FIRST = 262528
OLEOBJ_S_LAST = 262543
OLEOBJ_E_NOVERBS = -2147221120
OLEOBJ_E_INVALIDVERB = -2147221119
CLIENTSITE_E_FIRST = -2147221104
CLIENTSITE_E_LAST = -2147221089
CLIENTSITE_S_FIRST = 262544
CLIENTSITE_S_LAST = 262559
INPLACE_E_NOTUNDOABLE = -2147221088
INPLACE_E_NOTOOLSPACE = -2147221087
INPLACE_E_FIRST = -2147221088
INPLACE_E_LAST = -2147221073
INPLACE_S_FIRST = 262560
INPLACE_S_LAST = 262575
ENUM_E_FIRST = -2147221072
ENUM_E_LAST = -2147221057
ENUM_S_FIRST = 262576
ENUM_S_LAST = 262591
CONVERT10_E_FIRST = -2147221056
CONVERT10_E_LAST = -2147221041
CONVERT10_S_FIRST = 262592
CONVERT10_S_LAST = 262607
CONVERT10_E_OLESTREAM_GET = -2147221056
CONVERT10_E_OLESTREAM_PUT = -2147221055
CONVERT10_E_OLESTREAM_FMT = -2147221054
CONVERT10_E_OLESTREAM_BITMAP_TO_DIB = -2147221053
CONVERT10_E_STG_FMT = -2147221052
CONVERT10_E_STG_NO_STD_STREAM = -2147221051
CONVERT10_E_STG_DIB_TO_BITMAP = -2147221050
CLIPBRD_E_FIRST = -2147221040
CLIPBRD_E_LAST = -2147221025
CLIPBRD_S_FIRST = 262608
CLIPBRD_S_LAST = 262623
CLIPBRD_E_CANT_OPEN = -2147221040
CLIPBRD_E_CANT_EMPTY = -2147221039
CLIPBRD_E_CANT_SET = -2147221038
CLIPBRD_E_BAD_DATA = -2147221037
CLIPBRD_E_CANT_CLOSE = -2147221036
MK_E_FIRST = -2147221024
MK_E_LAST = -2147221009
MK_S_FIRST = 262624
MK_S_LAST = 262639
MK_E_CONNECTMANUALLY = -2147221024
MK_E_EXCEEDEDDEADLINE = -2147221023
MK_E_NEEDGENERIC = -2147221022
MK_E_UNAVAILABLE = -2147221021
MK_E_SYNTAX = -2147221020
MK_E_NOOBJECT = -2147221019
MK_E_INVALIDEXTENSION = -2147221018
MK_E_INTERMEDIATEINTERFACENOTSUPPORTED = -2147221017
MK_E_NOTBINDABLE = -2147221016
MK_E_NOTBOUND = -2147221015
MK_E_CANTOPENFILE = -2147221014
MK_E_MUSTBOTHERUSER = -2147221013
MK_E_NOINVERSE = -2147221012
MK_E_NOSTORAGE = -2147221011
MK_E_NOPREFIX = -2147221010
MK_E_ENUMERATION_FAILED = -2147221009
CO_E_FIRST = -2147221008
CO_E_LAST = -2147220993
CO_S_FIRST = 262640
CO_S_LAST = 262655
CO_E_NOTINITIALIZED = -2147221008
CO_E_ALREADYINITIALIZED = -2147221007
CO_E_CANTDETERMINECLASS = -2147221006
CO_E_CLASSSTRING = -2147221005
CO_E_IIDSTRING = -2147221004
CO_E_APPNOTFOUND = -2147221003
CO_E_APPSINGLEUSE = -2147221002
CO_E_ERRORINAPP = -2147221001
CO_E_DLLNOTFOUND = -2147221000
CO_E_ERRORINDLL = -2147220999
CO_E_WRONGOSFORAPP = -2147220998
CO_E_OBJNOTREG = -2147220997
CO_E_OBJISREG = -2147220996
CO_E_OBJNOTCONNECTED = -2147220995
CO_E_APPDIDNTREG = -2147220994
CO_E_RELEASED = -2147220993
CO_E_FAILEDTOIMPERSONATE = -2147220992
CO_E_FAILEDTOGETSECCTX = -2147220991
CO_E_FAILEDTOOPENTHREADTOKEN = -2147220990
CO_E_FAILEDTOGETTOKENINFO = -2147220989
CO_E_TRUSTEEDOESNTMATCHCLIENT = -2147220988
CO_E_FAILEDTOQUERYCLIENTBLANKET = -2147220987
CO_E_FAILEDTOSETDACL = -2147220986
CO_E_ACCESSCHECKFAILED = -2147220985
CO_E_NETACCESSAPIFAILED = -2147220984
CO_E_WRONGTRUSTEENAMESYNTAX = -2147220983
CO_E_INVALIDSID = -2147220982
CO_E_CONVERSIONFAILED = -2147220981
CO_E_NOMATCHINGSIDFOUND = -2147220980
CO_E_LOOKUPACCSIDFAILED = -2147220979
CO_E_NOMATCHINGNAMEFOUND = -2147220978
CO_E_LOOKUPACCNAMEFAILED = -2147220977
CO_E_SETSERLHNDLFAILED = -2147220976
CO_E_FAILEDTOGETWINDIR = -2147220975
CO_E_PATHTOOLONG = -2147220974
CO_E_FAILEDTOGENUUID = -2147220973
CO_E_FAILEDTOCREATEFILE = -2147220972
CO_E_FAILEDTOCLOSEHANDLE = -2147220971
CO_E_EXCEEDSYSACLLIMIT = -2147220970
CO_E_ACESINWRONGORDER = -2147220969
CO_E_INCOMPATIBLESTREAMVERSION = -2147220968
CO_E_FAILEDTOOPENPROCESSTOKEN = -2147220967
CO_E_DECODEFAILED = -2147220966
CO_E_ACNOTINITIALIZED = -2147220965
OLE_S_USEREG = 262144
OLE_S_STATIC = 262145
OLE_S_MAC_CLIPFORMAT = 262146
DRAGDROP_S_DROP = 262400
DRAGDROP_S_CANCEL = 262401
DRAGDROP_S_USEDEFAULTCURSORS = 262402
DATA_S_SAMEFORMATETC = 262448
VIEW_S_ALREADY_FROZEN = 262464
CACHE_S_FORMATETC_NOTSUPPORTED = 262512
CACHE_S_SAMECACHE = 262513
CACHE_S_SOMECACHES_NOTUPDATED = 262514
OLEOBJ_S_INVALIDVERB = 262528
OLEOBJ_S_CANNOT_DOVERB_NOW = 262529
OLEOBJ_S_INVALIDHWND = 262530
INPLACE_S_TRUNCATED = 262560
CONVERT10_S_NO_PRESENTATION = 262592
MK_S_REDUCED_TO_SELF = 262626
MK_S_ME = 262628
MK_S_HIM = 262629
MK_S_US = 262630
MK_S_MONIKERALREADYREGISTERED = 262631
CO_E_CLASS_CREATE_FAILED = -2146959359
CO_E_SCM_ERROR = -2146959358
CO_E_SCM_RPC_FAILURE = -2146959357
CO_E_BAD_PATH = -2146959356
CO_E_SERVER_EXEC_FAILURE = -2146959355
CO_E_OBJSRV_RPC_FAILURE = -2146959354
MK_E_NO_NORMALIZED = -2146959353
CO_E_SERVER_STOPPING = -2146959352
MEM_E_INVALID_ROOT = -2146959351
MEM_E_INVALID_LINK = -2146959344
MEM_E_INVALID_SIZE = -2146959343
CO_S_NOTALLINTERFACES = 524306
DISP_E_UNKNOWNINTERFACE = -2147352575
DISP_E_MEMBERNOTFOUND = -2147352573
DISP_E_PARAMNOTFOUND = -2147352572
DISP_E_TYPEMISMATCH = -2147352571
DISP_E_UNKNOWNNAME = -2147352570
DISP_E_NONAMEDARGS = -2147352569
DISP_E_BADVARTYPE = -2147352568
DISP_E_EXCEPTION = -2147352567
DISP_E_OVERFLOW = -2147352566
DISP_E_BADINDEX = -2147352565
DISP_E_UNKNOWNLCID = -2147352564
DISP_E_ARRAYISLOCKED = -2147352563
DISP_E_BADPARAMCOUNT = -2147352562
DISP_E_PARAMNOTOPTIONAL = -2147352561
DISP_E_BADCALLEE = -2147352560
DISP_E_NOTACOLLECTION = -2147352559
DISP_E_DIVBYZERO = -2147352558
TYPE_E_BUFFERTOOSMALL = -2147319786
TYPE_E_FIELDNOTFOUND = -2147319785
TYPE_E_INVDATAREAD = -2147319784
TYPE_E_UNSUPFORMAT = -2147319783
TYPE_E_REGISTRYACCESS = -2147319780
TYPE_E_LIBNOTREGISTERED = -2147319779
TYPE_E_UNDEFINEDTYPE = -2147319769
TYPE_E_QUALIFIEDNAMEDISALLOWED = -2147319768
TYPE_E_INVALIDSTATE = -2147319767
TYPE_E_WRONGTYPEKIND = -2147319766
TYPE_E_ELEMENTNOTFOUND = -2147319765
TYPE_E_AMBIGUOUSNAME = -2147319764
TYPE_E_NAMECONFLICT = -2147319763
TYPE_E_UNKNOWNLCID = -2147319762
TYPE_E_DLLFUNCTIONNOTFOUND = -2147319761
TYPE_E_BADMODULEKIND = -2147317571
TYPE_E_SIZETOOBIG = -2147317563
TYPE_E_DUPLICATEID = -2147317562
TYPE_E_INVALIDID = -2147317553
TYPE_E_TYPEMISMATCH = -2147316576
TYPE_E_OUTOFBOUNDS = -2147316575
TYPE_E_IOERROR = -2147316574
TYPE_E_CANTCREATETMPFILE = -2147316573
TYPE_E_CANTLOADLIBRARY = -2147312566
TYPE_E_INCONSISTENTPROPFUNCS = -2147312509
TYPE_E_CIRCULARTYPE = -2147312508
STG_E_INVALIDFUNCTION = -2147287039
STG_E_FILENOTFOUND = -2147287038
STG_E_PATHNOTFOUND = -2147287037
STG_E_TOOMANYOPENFILES = -2147287036
STG_E_ACCESSDENIED = -2147287035
STG_E_INVALIDHANDLE = -2147287034
STG_E_INSUFFICIENTMEMORY = -2147287032
STG_E_INVALIDPOINTER = -2147287031
STG_E_NOMOREFILES = -2147287022
STG_E_DISKISWRITEPROTECTED = -2147287021
STG_E_SEEKERROR = -2147287015
STG_E_WRITEFAULT = -2147287011
STG_E_READFAULT = -2147287010
STG_E_SHAREVIOLATION = -2147287008
STG_E_LOCKVIOLATION = -2147287007
STG_E_FILEALREADYEXISTS = -2147286960
STG_E_INVALIDPARAMETER = -2147286953
STG_E_MEDIUMFULL = -2147286928
STG_E_PROPSETMISMATCHED = -2147286800
STG_E_ABNORMALAPIEXIT = -2147286790
STG_E_INVALIDHEADER = -2147286789
STG_E_INVALIDNAME = -2147286788
STG_E_UNKNOWN = -2147286787
STG_E_UNIMPLEMENTEDFUNCTION = -2147286786
STG_E_INVALIDFLAG = -2147286785
STG_E_INUSE = -2147286784
STG_E_NOTCURRENT = -2147286783
STG_E_REVERTED = -2147286782
STG_E_CANTSAVE = -2147286781
STG_E_OLDFORMAT = -2147286780
STG_E_OLDDLL = -2147286779
STG_E_SHAREREQUIRED = -2147286778
STG_E_NOTFILEBASEDSTORAGE = -2147286777
STG_E_EXTANTMARSHALLINGS = -2147286776
STG_E_DOCFILECORRUPT = -2147286775
STG_E_BADBASEADDRESS = -2147286768
STG_E_INCOMPLETE = -2147286527
STG_E_TERMINATED = -2147286526
STG_S_CONVERTED = 197120
STG_S_BLOCK = 197121
STG_S_RETRYNOW = 197122
STG_S_MONITORING = 197123
STG_S_MULTIPLEOPENS = 197124
STG_S_CONSOLIDATIONFAILED = 197125
STG_S_CANNOTCONSOLIDATE = 197126
RPC_E_CALL_REJECTED = -2147418111
RPC_E_CALL_CANCELED = -2147418110
RPC_E_CANTPOST_INSENDCALL = -2147418109
RPC_E_CANTCALLOUT_INASYNCCALL = -2147418108
RPC_E_CANTCALLOUT_INEXTERNALCALL = -2147418107
RPC_E_CONNECTION_TERMINATED = -2147418106
RPC_E_SERVER_DIED = -2147418105
RPC_E_CLIENT_DIED = -2147418104
RPC_E_INVALID_DATAPACKET = -2147418103
RPC_E_CANTTRANSMIT_CALL = -2147418102
RPC_E_CLIENT_CANTMARSHAL_DATA = -2147418101
RPC_E_CLIENT_CANTUNMARSHAL_DATA = -2147418100
RPC_E_SERVER_CANTMARSHAL_DATA = -2147418099
RPC_E_SERVER_CANTUNMARSHAL_DATA = -2147418098
RPC_E_INVALID_DATA = -2147418097
RPC_E_INVALID_PARAMETER = -2147418096
RPC_E_CANTCALLOUT_AGAIN = -2147418095
RPC_E_SERVER_DIED_DNE = -2147418094
RPC_E_SYS_CALL_FAILED = -2147417856
RPC_E_OUT_OF_RESOURCES = -2147417855
RPC_E_ATTEMPTED_MULTITHREAD = -2147417854
RPC_E_NOT_REGISTERED = -2147417853
RPC_E_FAULT = -2147417852
RPC_E_SERVERFAULT = -2147417851
RPC_E_CHANGED_MODE = -2147417850
RPC_E_INVALIDMETHOD = -2147417849
RPC_E_DISCONNECTED = -2147417848
RPC_E_RETRY = -2147417847
RPC_E_SERVERCALL_RETRYLATER = -2147417846
RPC_E_SERVERCALL_REJECTED = -2147417845
RPC_E_INVALID_CALLDATA = -2147417844
RPC_E_CANTCALLOUT_ININPUTSYNCCALL = -2147417843
RPC_E_WRONG_THREAD = -2147417842
RPC_E_THREAD_NOT_INIT = -2147417841
RPC_E_VERSION_MISMATCH = -2147417840
RPC_E_INVALID_HEADER = -2147417839
RPC_E_INVALID_EXTENSION = -2147417838
RPC_E_INVALID_IPID = -2147417837
RPC_E_INVALID_OBJECT = -2147417836
RPC_S_CALLPENDING = -2147417835
RPC_S_WAITONTIMER = -2147417834
RPC_E_CALL_COMPLETE = -2147417833
RPC_E_UNSECURE_CALL = -2147417832
RPC_E_TOO_LATE = -2147417831
RPC_E_NO_GOOD_SECURITY_PACKAGES = -2147417830
RPC_E_ACCESS_DENIED = -2147417829
RPC_E_REMOTE_DISABLED = -2147417828
RPC_E_INVALID_OBJREF = -2147417827
RPC_E_NO_CONTEXT = -2147417826
RPC_E_TIMEOUT = -2147417825
RPC_E_NO_SYNC = -2147417824
RPC_E_UNEXPECTED = -2147352577
NTE_BAD_UID = -**********
NTE_BAD_HASH = -**********
NTE_BAD_KEY = -**********
NTE_BAD_LEN = -**********
NTE_BAD_DATA = -**********
NTE_BAD_SIGNATURE = -**********
NTE_BAD_VER = -**********
NTE_BAD_ALGID = -**********
NTE_BAD_FLAGS = -**********
NTE_BAD_TYPE = -**********
NTE_BAD_KEY_STATE = -**********
NTE_BAD_HASH_STATE = -**********
NTE_NO_KEY = -**********
NTE_NO_MEMORY = -**********
NTE_EXISTS = -**********
NTE_PERM = -**********
NTE_NOT_FOUND = -**********
NTE_DOUBLE_ENCRYPT = -**********
NTE_BAD_PROVIDER = -**********
NTE_BAD_PROV_TYPE = -**********
NTE_BAD_PUBLIC_KEY = -**********
NTE_BAD_KEYSET = -**********
NTE_PROV_TYPE_NOT_DEF = -**********
NTE_PROV_TYPE_ENTRY_BAD = -**********
NTE_KEYSET_NOT_DEF = -**********
NTE_KEYSET_ENTRY_BAD = -**********
NTE_PROV_TYPE_NO_MATCH = -**********
NTE_SIGNATURE_FILE_BAD = -**********
NTE_PROVIDER_DLL_FAIL = -**********
NTE_PROV_DLL_NOT_FOUND = -**********
NTE_BAD_KEYSET_PARAM = -**********
NTE_FAIL = -**********
NTE_SYS_ERR = -**********
CRYPT_E_MSG_ERROR = -**********
CRYPT_E_UNKNOWN_ALGO = -**********
CRYPT_E_OID_FORMAT = -**********
CRYPT_E_INVALID_MSG_TYPE = -**********
CRYPT_E_UNEXPECTED_ENCODING = -**********
CRYPT_E_AUTH_ATTR_MISSING = -**********
CRYPT_E_HASH_VALUE = -**********
CRYPT_E_INVALID_INDEX = -**********
CRYPT_E_ALREADY_DECRYPTED = -**********
CRYPT_E_NOT_DECRYPTED = -**********
CRYPT_E_RECIPIENT_NOT_FOUND = -**********
CRYPT_E_CONTROL_TYPE = -**********
CRYPT_E_ISSUER_SERIALNUMBER = -**********
CRYPT_E_SIGNER_NOT_FOUND = -**********
CRYPT_E_ATTRIBUTES_MISSING = -**********
CRYPT_E_STREAM_MSG_NOT_READY = -**********
CRYPT_E_STREAM_INSUFFICIENT_DATA = -**********
CRYPT_E_BAD_LEN = -**********
CRYPT_E_BAD_ENCODE = -**********
CRYPT_E_FILE_ERROR = -**********
CRYPT_E_NOT_FOUND = -**********
CRYPT_E_EXISTS = -**********
CRYPT_E_NO_PROVIDER = -**********
CRYPT_E_SELF_SIGNED = -**********
CRYPT_E_DELETED_PREV = -**********
CRYPT_E_NO_MATCH = -**********
CRYPT_E_UNEXPECTED_MSG_TYPE = -**********
CRYPT_E_NO_KEY_PROPERTY = -**********
CRYPT_E_NO_DECRYPT_CERT = -**********
CRYPT_E_BAD_MSG = -**********
CRYPT_E_NO_SIGNER = -**********
CRYPT_E_PENDING_CLOSE = -**********
CRYPT_E_REVOKED = -**********
CRYPT_E_NO_REVOCATION_DLL = -**********
CRYPT_E_NO_REVOCATION_CHECK = -**********
CRYPT_E_REVOCATION_OFFLINE = -**********
CRYPT_E_NOT_IN_REVOCATION_DATABASE = -**********
CRYPT_E_INVALID_NUMERIC_STRING = -**********
CRYPT_E_INVALID_PRINTABLE_STRING = -**********
CRYPT_E_INVALID_IA5_STRING = -**********
CRYPT_E_INVALID_X500_STRING = -**********
CRYPT_E_NOT_CHAR_STRING = -**********
CRYPT_E_FILERESIZED = -**********
CRYPT_E_SECURITY_SETTINGS = -**********
CRYPT_E_NO_VERIFY_USAGE_DLL = -**********
CRYPT_E_NO_VERIFY_USAGE_CHECK = -**********
CRYPT_E_VERIFY_USAGE_OFFLINE = -**********
CRYPT_E_NOT_IN_CTL = -**********
CRYPT_E_NO_TRUSTED_SIGNER = -2146885589
CRYPT_E_OSS_ERROR = -2146881536
CERTSRV_E_BAD_REQUESTSUBJECT = -**********
CERTSRV_E_NO_REQUEST = -**********
CERTSRV_E_BAD_REQUESTSTATUS = -**********
CERTSRV_E_PROPERTY_EMPTY = -**********
CERTDB_E_JET_ERROR = -**********
TRUST_E_SYSTEM_ERROR = -**********
TRUST_E_NO_SIGNER_CERT = -**********
TRUST_E_COUNTER_SIGNER = -**********
TRUST_E_CERT_SIGNATURE = -**********
TRUST_E_TIME_STAMP = -**********
TRUST_E_BAD_DIGEST = -**********
TRUST_E_BASIC_CONSTRAINTS = -**********
TRUST_E_FINANCIAL_CRITERIA = -**********
NTE_OP_OK = 0
TRUST_E_PROVIDER_UNKNOWN = -**********
TRUST_E_ACTION_UNKNOWN = -**********
TRUST_E_SUBJECT_FORM_UNKNOWN = -**********
TRUST_E_SUBJECT_NOT_TRUSTED = -**********
DIGSIG_E_ENCODE = -**********
DIGSIG_E_DECODE = -**********
DIGSIG_E_EXTENSIBILITY = -**********
DIGSIG_E_CRYPTO = -**********
PERSIST_E_SIZEDEFINITE = -**********
PERSIST_E_SIZEINDEFINITE = -**********
PERSIST_E_NOTSELFSIZING = -**********
TRUST_E_NOSIGNATURE = -**********
CERT_E_EXPIRED = -**********
CERT_E_VALIDITYPERIODNESTING = -**********
CERT_E_ROLE = -**********
CERT_E_PATHLENCONST = -**********
CERT_E_CRITICAL = -**********
CERT_E_PURPOSE = -**********
CERT_E_ISSUERCHAINING = -**********
CERT_E_MALFORMED = -**********
CERT_E_UNTRUSTEDROOT = -**********
CERT_E_CHAINING = -**********
TRUST_E_FAIL = -**********
CERT_E_REVOKED = -**********
CERT_E_UNTRUSTEDTESTROOT = -**********
CERT_E_REVOCATION_FAILURE = -**********
CERT_E_CN_NO_MATCH = -**********
CERT_E_WRONG_USAGE = -**********
SPAPI_E_EXPECTED_SECTION_NAME = -**********
SPAPI_E_BAD_SECTION_NAME_LINE = -2146500607
SPAPI_E_SECTION_NAME_TOO_LONG = -2146500606
SPAPI_E_GENERAL_SYNTAX = -2146500605
SPAPI_E_WRONG_INF_STYLE = -2146500352
SPAPI_E_SECTION_NOT_FOUND = -2146500351
SPAPI_E_LINE_NOT_FOUND = -2146500350
SPAPI_E_NO_ASSOCIATED_CLASS = -2146500096
SPAPI_E_CLASS_MISMATCH = -2146500095
SPAPI_E_DUPLICATE_FOUND = -2146500094
SPAPI_E_NO_DRIVER_SELECTED = -2146500093
SPAPI_E_KEY_DOES_NOT_EXIST = -2146500092
SPAPI_E_INVALID_DEVINST_NAME = -2146500091
SPAPI_E_INVALID_CLASS = -2146500090
SPAPI_E_DEVINST_ALREADY_EXISTS = -2146500089
SPAPI_E_DEVINFO_NOT_REGISTERED = -2146500088
SPAPI_E_INVALID_REG_PROPERTY = -2146500087
SPAPI_E_NO_INF = -2146500086
SPAPI_E_NO_SUCH_DEVINST = -2146500085
SPAPI_E_CANT_LOAD_CLASS_ICON = -2146500084
SPAPI_E_INVALID_CLASS_INSTALLER = -2146500083
SPAPI_E_DI_DO_DEFAULT = -2146500082
SPAPI_E_DI_NOFILECOPY = -2146500081
SPAPI_E_INVALID_HWPROFILE = -2146500080
SPAPI_E_NO_DEVICE_SELECTED = -2146500079
SPAPI_E_DEVINFO_LIST_LOCKED = -2146500078
SPAPI_E_DEVINFO_DATA_LOCKED = -2146500077
SPAPI_E_DI_BAD_PATH = -2146500076
SPAPI_E_NO_CLASSINSTALL_PARAMS = -2146500075
SPAPI_E_FILEQUEUE_LOCKED = -2146500074
SPAPI_E_BAD_SERVICE_INSTALLSECT = -2146500073
SPAPI_E_NO_CLASS_DRIVER_LIST = -2146500072
SPAPI_E_NO_ASSOCIATED_SERVICE = -**********
SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE = -**********
SPAPI_E_DEVICE_INTERFACE_ACTIVE = -**********
SPAPI_E_DEVICE_INTERFACE_REMOVED = -**********
SPAPI_E_BAD_INTERFACE_INSTALLSECT = -**********
SPAPI_E_NO_SUCH_INTERFACE_CLASS = -**********
SPAPI_E_INVALID_REFERENCE_STRING = -**********
SPAPI_E_INVALID_MACHINENAME = -**********
SPAPI_E_REMOTE_COMM_FAILURE = -**********
SPAPI_E_MACHINE_UNAVAILABLE = -**********
SPAPI_E_NO_CONFIGMGR_SERVICES = -**********
SPAPI_E_INVALID_PROPPAGE_PROVIDER = -**********
SPAPI_E_NO_SUCH_DEVICE_INTERFACE = -**********
SPAPI_E_DI_POSTPROCESSING_REQUIRED = -**********
SPAPI_E_INVALID_COINSTALLER = -**********
SPAPI_E_NO_COMPAT_DRIVERS = -**********
SPAPI_E_NO_DEVICE_ICON = -**********
SPAPI_E_INVALID_INF_LOGCONFIG = -**********
SPAPI_E_DI_DONT_INSTALL = -**********
SPAPI_E_INVALID_FILTER_DRIVER = -**********
SPAPI_E_ERROR_NOT_INSTALLED = -**********

# Directory storage
ERROR_DS_NOT_INSTALLED = 8200
ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY = 8201
ERROR_DS_NO_ATTRIBUTE_OR_VALUE = 8202
ERROR_DS_INVALID_ATTRIBUTE_SYNTAX = 8203
ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED = 8204
ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS = 8205
ERROR_DS_BUSY = 8206
ERROR_DS_UNAVAILABLE = 8207
ERROR_DS_NO_RIDS_ALLOCATED = 8208
ERROR_DS_NO_MORE_RIDS = 8209
ERROR_DS_INCORRECT_ROLE_OWNER = 8210
ERROR_DS_RIDMGR_INIT_ERROR = 8211
ERROR_DS_OBJ_CLASS_VIOLATION = 8212
ERROR_DS_CANT_ON_NON_LEAF = 8213
ERROR_DS_CANT_ON_RDN = 8214
ERROR_DS_CANT_MOD_OBJ_CLASS = 8215
ERROR_DS_CROSS_DOM_MOVE_ERROR = 8216
ERROR_DS_GC_NOT_AVAILABLE = 8217
ERROR_SHARED_POLICY = 8218
ERROR_POLICY_OBJECT_NOT_FOUND = 8219
ERROR_POLICY_ONLY_IN_DS = 8220
ERROR_PROMOTION_ACTIVE = 8221
ERROR_NO_PROMOTION_ACTIVE = 8222
ERROR_DS_OPERATIONS_ERROR = 8224
ERROR_DS_PROTOCOL_ERROR = 8225
ERROR_DS_TIMELIMIT_EXCEEDED = 8226
ERROR_DS_SIZELIMIT_EXCEEDED = 8227
ERROR_DS_ADMIN_LIMIT_EXCEEDED = 8228
ERROR_DS_COMPARE_FALSE = 8229
ERROR_DS_COMPARE_TRUE = 8230
ERROR_DS_AUTH_METHOD_NOT_SUPPORTED = 8231
ERROR_DS_STRONG_AUTH_REQUIRED = 8232
ERROR_DS_INAPPROPRIATE_AUTH = 8233
ERROR_DS_AUTH_UNKNOWN = 8234
ERROR_DS_REFERRAL = 8235
ERROR_DS_UNAVAILABLE_CRIT_EXTENSION = 8236
ERROR_DS_CONFIDENTIALITY_REQUIRED = 8237
ERROR_DS_INAPPROPRIATE_MATCHING = 8238
ERROR_DS_CONSTRAINT_VIOLATION = 8239
ERROR_DS_NO_SUCH_OBJECT = 8240
ERROR_DS_ALIAS_PROBLEM = 8241
ERROR_DS_INVALID_DN_SYNTAX = 8242
ERROR_DS_IS_LEAF = 8243
ERROR_DS_ALIAS_DEREF_PROBLEM = 8244
ERROR_DS_UNWILLING_TO_PERFORM = 8245
ERROR_DS_LOOP_DETECT = 8246
ERROR_DS_NAMING_VIOLATION = 8247
ERROR_DS_OBJECT_RESULTS_TOO_LARGE = 8248
ERROR_DS_AFFECTS_MULTIPLE_DSAS = 8249
ERROR_DS_SERVER_DOWN = 8250
ERROR_DS_LOCAL_ERROR = 8251
ERROR_DS_ENCODING_ERROR = 8252
ERROR_DS_DECODING_ERROR = 8253
ERROR_DS_FILTER_UNKNOWN = 8254
ERROR_DS_PARAM_ERROR = 8255
ERROR_DS_NOT_SUPPORTED = 8256
ERROR_DS_NO_RESULTS_RETURNED = 8257
ERROR_DS_CONTROL_NOT_FOUND = 8258
ERROR_DS_CLIENT_LOOP = 8259
ERROR_DS_REFERRAL_LIMIT_EXCEEDED = 8260
ERROR_DS_SORT_CONTROL_MISSING = 8261
ERROR_DS_OFFSET_RANGE_ERROR = 8262
ERROR_DS_ROOT_MUST_BE_NC = 8301
ERROR_DS_ADD_REPLICA_INHIBITED = 8302
ERROR_DS_ATT_NOT_DEF_IN_SCHEMA = 8303
ERROR_DS_MAX_OBJ_SIZE_EXCEEDED = 8304
ERROR_DS_OBJ_STRING_NAME_EXISTS = 8305
ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA = 8306
ERROR_DS_RDN_DOESNT_MATCH_SCHEMA = 8307
ERROR_DS_NO_REQUESTED_ATTS_FOUND = 8308
ERROR_DS_USER_BUFFER_TO_SMALL = 8309
ERROR_DS_ATT_IS_NOT_ON_OBJ = 8310
ERROR_DS_ILLEGAL_MOD_OPERATION = 8311
ERROR_DS_OBJ_TOO_LARGE = 8312
ERROR_DS_BAD_INSTANCE_TYPE = 8313
ERROR_DS_MASTERDSA_REQUIRED = 8314
ERROR_DS_OBJECT_CLASS_REQUIRED = 8315
ERROR_DS_MISSING_REQUIRED_ATT = 8316
ERROR_DS_ATT_NOT_DEF_FOR_CLASS = 8317
ERROR_DS_ATT_ALREADY_EXISTS = 8318
ERROR_DS_CANT_ADD_ATT_VALUES = 8320
ERROR_DS_SINGLE_VALUE_CONSTRAINT = 8321
ERROR_DS_RANGE_CONSTRAINT = 8322
ERROR_DS_ATT_VAL_ALREADY_EXISTS = 8323
ERROR_DS_CANT_REM_MISSING_ATT = 8324
ERROR_DS_CANT_REM_MISSING_ATT_VAL = 8325
ERROR_DS_ROOT_CANT_BE_SUBREF = 8326
ERROR_DS_NO_CHAINING = 8327
ERROR_DS_NO_CHAINED_EVAL = 8328
ERROR_DS_NO_PARENT_OBJECT = 8329
ERROR_DS_PARENT_IS_AN_ALIAS = 8330
ERROR_DS_CANT_MIX_MASTER_AND_REPS = 8331
ERROR_DS_CHILDREN_EXIST = 8332
ERROR_DS_OBJ_NOT_FOUND = 8333
ERROR_DS_ALIASED_OBJ_MISSING = 8334
ERROR_DS_BAD_NAME_SYNTAX = 8335
ERROR_DS_ALIAS_POINTS_TO_ALIAS = 8336
ERROR_DS_CANT_DEREF_ALIAS = 8337
ERROR_DS_OUT_OF_SCOPE = 8338
ERROR_DS_OBJECT_BEING_REMOVED = 8339
ERROR_DS_CANT_DELETE_DSA_OBJ = 8340
ERROR_DS_GENERIC_ERROR = 8341
ERROR_DS_DSA_MUST_BE_INT_MASTER = 8342
ERROR_DS_CLASS_NOT_DSA = 8343
ERROR_DS_INSUFF_ACCESS_RIGHTS = 8344
ERROR_DS_ILLEGAL_SUPERIOR = 8345
ERROR_DS_ATTRIBUTE_OWNED_BY_SAM = 8346
ERROR_DS_NAME_TOO_MANY_PARTS = 8347
ERROR_DS_NAME_TOO_LONG = 8348
ERROR_DS_NAME_VALUE_TOO_LONG = 8349
ERROR_DS_NAME_UNPARSEABLE = 8350
ERROR_DS_NAME_TYPE_UNKNOWN = 8351
ERROR_DS_NOT_AN_OBJECT = 8352
ERROR_DS_SEC_DESC_TOO_SHORT = 8353
ERROR_DS_SEC_DESC_INVALID = 8354
ERROR_DS_NO_DELETED_NAME = 8355
ERROR_DS_SUBREF_MUST_HAVE_PARENT = 8356
ERROR_DS_NCNAME_MUST_BE_NC = 8357
ERROR_DS_CANT_ADD_SYSTEM_ONLY = 8358
ERROR_DS_CLASS_MUST_BE_CONCRETE = 8359
ERROR_DS_INVALID_DMD = 8360
ERROR_DS_OBJ_GUID_EXISTS = 8361
ERROR_DS_NOT_ON_BACKLINK = 8362
ERROR_DS_NO_CROSSREF_FOR_NC = 8363
ERROR_DS_SHUTTING_DOWN = 8364
ERROR_DS_UNKNOWN_OPERATION = 8365
ERROR_DS_INVALID_ROLE_OWNER = 8366
ERROR_DS_COULDNT_CONTACT_FSMO = 8367
ERROR_DS_CROSS_NC_DN_RENAME = 8368
ERROR_DS_CANT_MOD_SYSTEM_ONLY = 8369
ERROR_DS_REPLICATOR_ONLY = 8370
ERROR_DS_OBJ_CLASS_NOT_DEFINED = 8371
ERROR_DS_OBJ_CLASS_NOT_SUBCLASS = 8372
ERROR_DS_NAME_REFERENCE_INVALID = 8373
ERROR_DS_CROSS_REF_EXISTS = 8374
ERROR_DS_CANT_DEL_MASTER_CROSSREF = 8375
ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD = 8376
ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX = 8377
ERROR_DS_DUP_RDN = 8378
ERROR_DS_DUP_OID = 8379
ERROR_DS_DUP_MAPI_ID = 8380
ERROR_DS_DUP_SCHEMA_ID_GUID = 8381
ERROR_DS_DUP_LDAP_DISPLAY_NAME = 8382
ERROR_DS_SEMANTIC_ATT_TEST = 8383
ERROR_DS_SYNTAX_MISMATCH = 8384
ERROR_DS_EXISTS_IN_MUST_HAVE = 8385
ERROR_DS_EXISTS_IN_MAY_HAVE = 8386
ERROR_DS_NONEXISTENT_MAY_HAVE = 8387
ERROR_DS_NONEXISTENT_MUST_HAVE = 8388
ERROR_DS_AUX_CLS_TEST_FAIL = 8389
ERROR_DS_NONEXISTENT_POSS_SUP = 8390
ERROR_DS_SUB_CLS_TEST_FAIL = 8391
ERROR_DS_BAD_RDN_ATT_ID_SYNTAX = 8392
ERROR_DS_EXISTS_IN_AUX_CLS = 8393
ERROR_DS_EXISTS_IN_SUB_CLS = 8394
ERROR_DS_EXISTS_IN_POSS_SUP = 8395
ERROR_DS_RECALCSCHEMA_FAILED = 8396
ERROR_DS_TREE_DELETE_NOT_FINISHED = 8397
ERROR_DS_CANT_DELETE = 8398
ERROR_DS_ATT_SCHEMA_REQ_ID = 8399
ERROR_DS_BAD_ATT_SCHEMA_SYNTAX = 8400
ERROR_DS_CANT_CACHE_ATT = 8401
ERROR_DS_CANT_CACHE_CLASS = 8402
ERROR_DS_CANT_REMOVE_ATT_CACHE = 8403
ERROR_DS_CANT_REMOVE_CLASS_CACHE = 8404
ERROR_DS_CANT_RETRIEVE_DN = 8405
ERROR_DS_MISSING_SUPREF = 8406
ERROR_DS_CANT_RETRIEVE_INSTANCE = 8407
ERROR_DS_CODE_INCONSISTENCY = 8408
ERROR_DS_DATABASE_ERROR = 8409
ERROR_DS_GOVERNSID_MISSING = 8410
ERROR_DS_MISSING_EXPECTED_ATT = 8411
ERROR_DS_NCNAME_MISSING_CR_REF = 8412
ERROR_DS_SECURITY_CHECKING_ERROR = 8413
ERROR_DS_SCHEMA_NOT_LOADED = 8414
ERROR_DS_SCHEMA_ALLOC_FAILED = 8415
ERROR_DS_ATT_SCHEMA_REQ_SYNTAX = 8416
ERROR_DS_GCVERIFY_ERROR = 8417
ERROR_DS_DRA_SCHEMA_MISMATCH = 8418
ERROR_DS_CANT_FIND_DSA_OBJ = 8419
ERROR_DS_CANT_FIND_EXPECTED_NC = 8420
ERROR_DS_CANT_FIND_NC_IN_CACHE = 8421
ERROR_DS_CANT_RETRIEVE_CHILD = 8422
ERROR_DS_SECURITY_ILLEGAL_MODIFY = 8423
ERROR_DS_CANT_REPLACE_HIDDEN_REC = 8424
ERROR_DS_BAD_HIERARCHY_FILE = 8425
ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED = 8426
ERROR_DS_CONFIG_PARAM_MISSING = 8427
ERROR_DS_COUNTING_AB_INDICES_FAILED = 8428
ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED = 8429
ERROR_DS_INTERNAL_FAILURE = 8430
ERROR_DS_UNKNOWN_ERROR = 8431
ERROR_DS_ROOT_REQUIRES_CLASS_TOP = 8432
ERROR_DS_REFUSING_FSMO_ROLES = 8433
ERROR_DS_MISSING_FSMO_SETTINGS = 8434
ERROR_DS_UNABLE_TO_SURRENDER_ROLES = 8435
ERROR_DS_DRA_GENERIC = 8436
ERROR_DS_DRA_INVALID_PARAMETER = 8437
ERROR_DS_DRA_BUSY = 8438
ERROR_DS_DRA_BAD_DN = 8439
ERROR_DS_DRA_BAD_NC = 8440
ERROR_DS_DRA_DN_EXISTS = 8441
ERROR_DS_DRA_INTERNAL_ERROR = 8442
ERROR_DS_DRA_INCONSISTENT_DIT = 8443
ERROR_DS_DRA_CONNECTION_FAILED = 8444
ERROR_DS_DRA_BAD_INSTANCE_TYPE = 8445
ERROR_DS_DRA_OUT_OF_MEM = 8446
ERROR_DS_DRA_MAIL_PROBLEM = 8447
ERROR_DS_DRA_REF_ALREADY_EXISTS = 8448
ERROR_DS_DRA_REF_NOT_FOUND = 8449
ERROR_DS_DRA_OBJ_IS_REP_SOURCE = 8450
ERROR_DS_DRA_DB_ERROR = 8451
ERROR_DS_DRA_NO_REPLICA = 8452
ERROR_DS_DRA_ACCESS_DENIED = 8453
ERROR_DS_DRA_NOT_SUPPORTED = 8454
ERROR_DS_DRA_RPC_CANCELLED = 8455
ERROR_DS_DRA_SOURCE_DISABLED = 8456
ERROR_DS_DRA_SINK_DISABLED = 8457
ERROR_DS_DRA_NAME_COLLISION = 8458
ERROR_DS_DRA_SOURCE_REINSTALLED = 8459
ERROR_DS_DRA_MISSING_PARENT = 8460
ERROR_DS_DRA_PREEMPTED = 8461
ERROR_DS_DRA_ABANDON_SYNC = 8462
ERROR_DS_DRA_SHUTDOWN = 8463
ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET = 8464
ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA = 8465
ERROR_DS_DRA_EXTN_CONNECTION_FAILED = 8466
ERROR_DS_INSTALL_SCHEMA_MISMATCH = 8467
ERROR_DS_DUP_LINK_ID = 8468
ERROR_DS_NAME_ERROR_RESOLVING = 8469
ERROR_DS_NAME_ERROR_NOT_FOUND = 8470
ERROR_DS_NAME_ERROR_NOT_UNIQUE = 8471
ERROR_DS_NAME_ERROR_NO_MAPPING = 8472
ERROR_DS_NAME_ERROR_DOMAIN_ONLY = 8473
ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING = 8474
ERROR_DS_CONSTRUCTED_ATT_MOD = 8475
ERROR_DS_WRONG_OM_OBJ_CLASS = 8476
ERROR_DS_DRA_REPL_PENDING = 8477
ERROR_DS_DS_REQUIRED = 8478
ERROR_DS_INVALID_LDAP_DISPLAY_NAME = 8479
ERROR_DS_NON_BASE_SEARCH = 8480
ERROR_DS_CANT_RETRIEVE_ATTS = 8481
ERROR_DS_BACKLINK_WITHOUT_LINK = 8482
ERROR_DS_EPOCH_MISMATCH = 8483
ERROR_DS_SRC_NAME_MISMATCH = 8484
ERROR_DS_SRC_AND_DST_NC_IDENTICAL = 8485
ERROR_DS_DST_NC_MISMATCH = 8486
ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC = 8487
ERROR_DS_SRC_GUID_MISMATCH = 8488
ERROR_DS_CANT_MOVE_DELETED_OBJECT = 8489
ERROR_DS_PDC_OPERATION_IN_PROGRESS = 8490
ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD = 8491
ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION = 8492
ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS = 8493
ERROR_DS_NC_MUST_HAVE_NC_PARENT = 8494
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE = 8495
ERROR_DS_DST_DOMAIN_NOT_NATIVE = 8496
ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER = 8497
ERROR_DS_CANT_MOVE_ACCOUNT_GROUP = 8498
ERROR_DS_CANT_MOVE_RESOURCE_GROUP = 8499
ERROR_DS_INVALID_SEARCH_FLAG = 8500
ERROR_DS_NO_TREE_DELETE_ABOVE_NC = 8501
ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE = 8502
ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE = 8503
ERROR_DS_SAM_INIT_FAILURE = 8504
ERROR_DS_SENSITIVE_GROUP_VIOLATION = 8505
ERROR_DS_CANT_MOD_PRIMARYGROUPID = 8506
ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD = 8507
ERROR_DS_NONSAFE_SCHEMA_CHANGE = 8508
ERROR_DS_SCHEMA_UPDATE_DISALLOWED = 8509
ERROR_DS_CANT_CREATE_UNDER_SCHEMA = 8510
ERROR_DS_INSTALL_NO_SRC_SCH_VERSION = 8511
ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE = 8512
ERROR_DS_INVALID_GROUP_TYPE = 8513
ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN = 8514
ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN = 8515
ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER = 8516
ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER = 8517
ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER = 8518
ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER = 8519
ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER = 8520
ERROR_DS_HAVE_PRIMARY_MEMBERS = 8521
ERROR_DS_STRING_SD_CONVERSION_FAILED = 8522
ERROR_DS_NAMING_MASTER_GC = 8523
ERROR_DS_DNS_LOOKUP_FAILURE = 8524
ERROR_DS_COULDNT_UPDATE_SPNS = 8525
ERROR_DS_CANT_RETRIEVE_SD = 8526
ERROR_DS_KEY_NOT_UNIQUE = 8527
ERROR_DS_WRONG_LINKED_ATT_SYNTAX = 8528
ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD = 8529
ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY = 8530
ERROR_DS_CANT_START = 8531
ERROR_DS_INIT_FAILURE = 8532
ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION = 8533
ERROR_DS_SOURCE_DOMAIN_IN_FOREST = 8534
ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST = 8535
ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED = 8536
ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN = 8537
ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER = 8538
ERROR_DS_SRC_SID_EXISTS_IN_FOREST = 8539
ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH = 8540
ERROR_SAM_INIT_FAILURE = 8541
ERROR_DS_DRA_SCHEMA_INFO_SHIP = 8542
ERROR_DS_DRA_SCHEMA_CONFLICT = 8543
ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT = 8544
ERROR_DS_DRA_OBJ_NC_MISMATCH = 8545
ERROR_DS_NC_STILL_HAS_DSAS = 8546
ERROR_DS_GC_REQUIRED = 8547
ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY = 8548
ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS = 8549
ERROR_DS_CANT_ADD_TO_GC = 8550
ERROR_DS_NO_CHECKPOINT_WITH_PDC = 8551
ERROR_DS_SOURCE_AUDITING_NOT_ENABLED = 8552
ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC = 8553
ERROR_DS_INVALID_NAME_FOR_SPN = 8554
ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS = 8555
ERROR_DS_UNICODEPWD_NOT_IN_QUOTES = 8556
ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED = 8557
ERROR_DS_MUST_BE_RUN_ON_DST_DC = 8558
ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER = 8559
ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ = 8560
ERROR_DS_INIT_FAILURE_CONSOLE = 8561
ERROR_DS_SAM_INIT_FAILURE_CONSOLE = 8562
ERROR_DS_FOREST_VERSION_TOO_HIGH = 8563
ERROR_DS_DOMAIN_VERSION_TOO_HIGH = 8564
ERROR_DS_FOREST_VERSION_TOO_LOW = 8565
ERROR_DS_DOMAIN_VERSION_TOO_LOW = 8566
ERROR_DS_INCOMPATIBLE_VERSION = 8567
ERROR_DS_LOW_DSA_VERSION = 8568
ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN = 8569
ERROR_DS_NOT_SUPPORTED_SORT_ORDER = 8570
ERROR_DS_NAME_NOT_UNIQUE = 8571
ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 = 8572
ERROR_DS_OUT_OF_VERSION_STORE = 8573
ERROR_DS_INCOMPATIBLE_CONTROLS_USED = 8574
ERROR_DS_NO_REF_DOMAIN = 8575
ERROR_DS_RESERVED_LINK_ID = 8576
ERROR_DS_LINK_ID_NOT_AVAILABLE = 8577
ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER = 8578
ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE = 8579
ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC = 8580
ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG = 8581
ERROR_DS_MODIFYDN_WRONG_GRANDPARENT = 8582
ERROR_DS_NAME_ERROR_TRUST_REFERRAL = 8583
ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER = 8584
ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD = 8585
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 = 8586
ERROR_DS_THREAD_LIMIT_EXCEEDED = 8587
ERROR_DS_NOT_CLOSEST = 8588
ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF = 8589
ERROR_DS_SINGLE_USER_MODE_FAILED = 8590
ERROR_DS_NTDSCRIPT_SYNTAX_ERROR = 8591
ERROR_DS_NTDSCRIPT_PROCESS_ERROR = 8592
ERROR_DS_DIFFERENT_REPL_EPOCHS = 8593
ERROR_DS_DRS_EXTENSIONS_CHANGED = 8594
ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR = 8595
ERROR_DS_NO_MSDS_INTID = 8596
ERROR_DS_DUP_MSDS_INTID = 8597
ERROR_DS_EXISTS_IN_RDNATTID = 8598
ERROR_DS_AUTHORIZATION_FAILED = 8599
ERROR_DS_INVALID_SCRIPT = 8600
ERROR_DS_REMOTE_CROSSREF_OP_FAILED = 8601
ERROR_DS_CROSS_REF_BUSY = 8602
ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN = 8603
ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC = 8604
ERROR_DS_DUPLICATE_ID_FOUND = 8605
ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT = 8606
ERROR_DS_GROUP_CONVERSION_ERROR = 8607
ERROR_DS_CANT_MOVE_APP_BASIC_GROUP = 8608
ERROR_DS_CANT_MOVE_APP_QUERY_GROUP = 8609
ERROR_DS_ROLE_NOT_VERIFIED = 8610
ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL = 8611
ERROR_DS_DOMAIN_RENAME_IN_PROGRESS = 8612
ERROR_DS_EXISTING_AD_CHILD_NC = 8613
ERROR_DS_REPL_LIFETIME_EXCEEDED = 8614
ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER = 8615
ERROR_DS_LDAP_SEND_QUEUE_FULL = 8616
ERROR_DS_DRA_OUT_SCHEDULE_WINDOW = 8617

# Common dialog box error codes from cderr.h
CDERR_DIALOGFAILURE = 65535
CDERR_GENERALCODES = 0
CDERR_STRUCTSIZE = 1
CDERR_INITIALIZATION = 2
CDERR_NOTEMPLATE = 3
CDERR_NOHINSTANCE = 4
CDERR_LOADSTRFAILURE = 5
CDERR_FINDRESFAILURE = 6
CDERR_LOADRESFAILURE = 7
CDERR_LOCKRESFAILURE = 8
CDERR_MEMALLOCFAILURE = 9
CDERR_MEMLOCKFAILURE = 10
CDERR_NOHOOK = 11
CDERR_REGISTERMSGFAIL = 12
PDERR_PRINTERCODES = 4096
PDERR_SETUPFAILURE = 4097
PDERR_PARSEFAILURE = 4098
PDERR_RETDEFFAILURE = 4099
PDERR_LOADDRVFAILURE = 4100
PDERR_GETDEVMODEFAIL = 4101
PDERR_INITFAILURE = 4102
PDERR_NODEVICES = 4103
PDERR_NODEFAULTPRN = 4104
PDERR_DNDMMISMATCH = 4105
PDERR_CREATEICFAILURE = 4106
PDERR_PRINTERNOTFOUND = 4107
PDERR_DEFAULTDIFFERENT = 4108
CFERR_CHOOSEFONTCODES = 8192
CFERR_NOFONTS = 8193
CFERR_MAXLESSTHANMIN = 8194
FNERR_FILENAMECODES = 12288
FNERR_SUBCLASSFAILURE = 12289
FNERR_INVALIDFILENAME = 12290
FNERR_BUFFERTOOSMALL = 12291
FRERR_FINDREPLACECODES = 16384
FRERR_BUFFERLENGTHZERO = 16385
CCERR_CHOOSECOLORCODES = 20480
