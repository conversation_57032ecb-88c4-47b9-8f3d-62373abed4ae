# --------------------------------------------------------------------------------
# 这是我们重构后的 screen.py 文件
# 我们用更稳定、更高效的 mss 库替换了原来有问题的 bettercam 库
# --------------------------------------------------------------------------------
import cv2
import mss  # 导入我们新的屏幕捕捉引擎 mss
import numpy as np
import win32api # 用于获取屏幕分辨率

class Screen:
    # 类的初始化方法，当创建 Screen 对象时会自动调用
    def __init__(self, config):
        self.config = config  # 保存传入的配置信息

        # --- 使用 mss 进行初始化 ---
        self.sct = mss.mss()  # 创建一个 mss 的实例，这是我们的截图工具

        # --- FOV 和分辨率设置 ---
        # 决定截图区域（FOV, Field of View）
        if self.config.auto_detect_resolution:
            # 自动获取主屏幕的分辨率
            self.screen_width = win32api.GetSystemMetrics(0)
            self.screen_height = win32api.GetSystemMetrics(1)
        else:
            # 使用配置文件中指定的分辨率
            self.screen_width = self.config.resolution_x
            self.screen_height = self.config.resolution_y

        # 计算截图区域的左上角坐标，使其始终在屏幕中央
        self.monitor = {
            "top": int((self.screen_height - self.config.capture_fov_y) / 2),
            "left": int((self.screen_width - self.config.capture_fov_x) / 2),
            "width": self.config.capture_fov_x,
            "height": self.config.capture_fov_y,
        }

        # --- 颜色范围设置 ---
        # 将配置文件中的颜色字符串转换为Numpy数组，这是OpenCV要求的格式
        self.lower_color = np.array(self.config.lower_color)
        self.upper_color = np.array(self.config.upper_color)

    # 这是类的核心方法，负责获取目标
    def get_target(self, recoil_offset):
        # 使用 self.sct.grab() 来截取指定区域的屏幕
        sct_img = self.sct.grab(self.monitor)
        
        # 将 mss 截图对象转换为 OpenCV 能处理的 Numpy 数组
        # mss 的输出是 BGRA 格式（蓝、绿、红、透明度），我们需要转换为 BGR
        frame = cv2.cvtColor(np.array(sct_img), cv2.COLOR_BGRA2BGR)
        
        # 将图像从 BGR 颜色空间转换为 HSV 颜色空间，便于颜色筛选
        hsv_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 根据我们设定的颜色范围，生成一个黑白“掩码”(mask)
        # 在掩码中，目标颜色区域是白色，其他都是黑色
        mask = cv2.inRange(hsv_frame, self.lower_color, self.upper_color)
        # 对掩码进行按位非操作，也就是黑白反转
        mask = cv2.bitwise_not(mask)

        # 在掩码上寻找所有物体的轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 检查是否找到了任何轮廓
        if contours:
            # 如果找到了，就找出其中面积最大的那个轮廓
            best_contour = max(contours, key=cv2.contourArea)
            
            # 计算这个最大轮廓的边界框 (x, y, 宽度, 高度)
            x, y, w, h = cv2.boundingRect(best_contour)

            # --- 这是 Triggerbot 的逻辑 ---
            # 检查这个最大的目标是否接触到了我们瞄准区域的中心
            trigger = False
            center_x = self.config.aim_fov_x / 2
            center_y = self.config.aim_fov_y / 2
            if (x < center_x < x + w) and (y < center_y < y + h):
                trigger = True

            # --- 这是 Aimbot 的逻辑 ---
            # 计算目标的中心点坐标
            target_center_x = x + w / 2
            target_center_y = y + h * self.config.aim_height # aim_height可以调整瞄准头部还是身体
            
            # 将目标坐标打包成一个元组(tuple)返回
            target = (target_center_x, target_center_y, w, h)

            # 如果开启了调试模式，就显示调试窗口
            if self.config.debug:
                if self.config.display_mode == 'mask':
                    # 在掩码上画出准星和找到的目标
                    cv2.circle(mask, (int(target_center_x), int(target_center_y)), 5, (200, 200, 200), -1)
                    cv2.rectangle(mask, (x, y), (x + w, y + h), (200, 200, 200), 2)
                    cv2.imshow("Unibot Mask", mask)
                else: # 'game' mode
                    # 在原始截图上画出准星和找到的目标
                    cv2.circle(frame, (int(target_center_x), int(target_center_y)), 5, (0, 255, 0), -1)
                    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
                    cv2.imshow("Unibot", frame)
                cv2.waitKey(1) # 这是让窗口能够显示的关键

            return target, trigger

        # 如果没有找到任何轮廓，就返回 None
        # 同时，如果开启了调试，也要显示一下窗口（虽然是全黑的）
        if self.config.debug:
            if self.config.display_mode == 'mask':
                cv2.imshow("Unibot Mask", mask)
            else:
                cv2.imshow("Unibot", frame)
            cv2.waitKey(1)

        return None, False

    # 当程序结束时，确保关闭所有窗口
    def __del__(self):
        cv2.destroyAllWindows()