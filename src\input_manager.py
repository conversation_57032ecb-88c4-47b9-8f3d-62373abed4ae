"""
    Input Manager - Advanced input handling system
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""


def get_input_implementation(config):
    """Get the appropriate input implementation based on configuration."""
    method = config.input_method

    if method == 'winapi':
        print('Using WinAPI Input Handler')
        from mouse.winapi_input import WinApiInputHandler
        return WinApiInputHandler(config)
    elif method == 'interception_driver':
        print('Using Interception Input Handler')
        from mouse.interception_input import InterceptionInputHandler
        return InterceptionInputHandler(config)
    elif method == 'microcontroller_serial':
        print('Using Serial Input Handler')
        from mouse.serial_input import SerialInputHandler
        return SerialInputHandler(config)
    elif method == 'microcontroller_socket':
        print('Using Socket Input Handler')
        from mouse.socket_input import SocketInputHandler
        return SocketInputHandler(config)
    else:
        raise ValueError(f"Unknown input method: {method}")
