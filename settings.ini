; DisplayHelper - Advanced Visual Processing System Configuration
; For detailed configuration guide, see documentation

[enhancement]
; Input methods: winapi, interception_driver, microcontroller_serial, microcontroller_socket
input_method = winapi
display_center_offset = 0
movement_smoothing_factor = 0.0
movement_speed = 1.0
vertical_speed_multiplier = 1.0
target_height_ratio = 0.5

[device]
; Device communication settings (for microcontroller modes)
device_ip = 0.0.0.0
device_port = 50256
serial_port = COM1

[visual]
target_grouping_threshold = 3, 3

; HSV color range for target detection
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150

capture_region_x = 256
capture_region_y = 256
processing_region_x = 256
processing_region_y = 256
max_frames_per_sec = 60
auto_detect_resolution = true
resolution_x = 1920
resolution_y = 1080

[compensation]
mode = move
compensation_x = 0.0
compensation_y = 0.0
max_offset = 100
recovery_rate = 0.0

[action]
; Base delay for automatic actions
action_delay = 0
; Additional random delay, does nothing if action_delay is 0
action_randomization = 30
action_threshold = 8

[input_rate]
; Maximum input rate per second
target_rate = 10

[keys]
; Virtual key codes: https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes
; F1
key_reload_settings = 0x70
; F2
key_toggle_enhancement = 0x71
; F3
key_toggle_compensation = 0x72
; F4
key_system_exit = 0x73
; Mouse 4
key_auto_action = 0x06
; Mouse 5
key_rapid_input = 0x05
; Mouse1 & Mouse2 (set to 'off' to disable activation requirement)
enhancement_activation_keys = 0x06, 0x02

[debug]
; Show debug visualization
enabled = true
; Refresh debug display even when enhancement is off
always_on = true
; 'game' shows actual screen, 'mask' shows processed view
display_mode = mask
