"""
    Behavior Randomizer - Human-like behavior simulation
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import random
import math
import time
import numpy as np
from typing import Tuple, Optional


class BehaviorRandomizer:
    """Provides human-like behavior randomization for natural input simulation."""
    
    def __init__(self):
        """Initialize the behavior randomizer."""
        # Seed random number generator with current time for better randomness
        random.seed(int(time.time() * 1000000) % 2**32)
        np.random.seed(int(time.time() * 1000000) % 2**32)
        
        # Human behavior parameters
        self.base_reaction_time = 0.15  # 150ms average human reaction time
        self.reaction_variance = 0.05   # ±50ms variance
        self.fatigue_factor = 1.0       # Increases over time
        self.last_action_time = time.time()
        
        # Movement parameters
        self.movement_jitter_scale = 0.8
        self.micro_correction_probability = 0.15
        self.overshoot_probability = 0.08
        
    def get_human_reaction_time(self) -> float:
        """Generate human-like reaction time using normal distribution."""
        # Simulate fatigue - slower reactions over time
        fatigue_adjustment = min(1.2, 1.0 + (time.time() - self.last_action_time) / 3600 * 0.1)
        
        # Generate reaction time with normal distribution
        reaction_time = np.random.normal(
            self.base_reaction_time * fatigue_adjustment,
            self.reaction_variance
        )
        
        # Clamp to reasonable bounds (50ms to 500ms)
        return max(0.05, min(0.5, reaction_time))
    
    def get_action_delay(self, base_delay: int, randomization: int) -> float:
        """Generate action delay with improved randomization."""
        if base_delay == 0:
            return 0
            
        # Use exponential distribution for more natural timing
        if randomization > 0:
            # Generate exponential random value
            exp_random = np.random.exponential(randomization / 3)
            # Clamp to prevent extreme values
            exp_random = min(exp_random, randomization * 2)
            delay = (base_delay + exp_random) / 1000
        else:
            delay = base_delay / 1000
            
        # Add small human reaction component
        delay += self.get_human_reaction_time() * 0.3
        
        return max(0.01, delay)  # Minimum 10ms delay
    
    def get_click_timing(self) -> Tuple[float, float]:
        """Generate human-like click timing (press duration and release delay)."""
        # Human click duration varies between 40-120ms
        press_duration = np.random.gamma(2, 0.025) + 0.04  # Gamma distribution for natural variation
        press_duration = min(0.12, max(0.04, press_duration))
        
        # Small delay after release (25-60ms)
        release_delay = np.random.gamma(1.5, 0.015) + 0.025
        release_delay = min(0.06, max(0.025, release_delay))
        
        return press_duration, release_delay
    
    def add_movement_jitter(self, x: float, y: float) -> Tuple[float, float]:
        """Add human-like micro-movements and jitter to cursor movement."""
        # Base jitter using normal distribution
        jitter_x = np.random.normal(0, self.movement_jitter_scale)
        jitter_y = np.random.normal(0, self.movement_jitter_scale)
        
        # Occasional micro-corrections (small opposite movements)
        if random.random() < self.micro_correction_probability:
            correction_scale = 0.3
            jitter_x += np.random.normal(0, correction_scale) * (-1 if x > 0 else 1)
            jitter_y += np.random.normal(0, correction_scale) * (-1 if y > 0 else 1)
        
        # Occasional slight overshoot
        if random.random() < self.overshoot_probability:
            overshoot_factor = 1.0 + np.random.uniform(0.05, 0.15)
            x *= overshoot_factor
            y *= overshoot_factor
        
        return x + jitter_x, y + jitter_y
    
    def get_movement_curve_points(self, start_x: float, start_y: float, 
                                 end_x: float, end_y: float, 
                                 num_points: int = 5) -> list:
        """Generate curved movement path with human-like characteristics."""
        if num_points < 2:
            return [(end_x, end_y)]
        
        points = []
        
        # Calculate distance for curve intensity
        distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
        
        # Add curve variation based on distance
        curve_intensity = min(distance * 0.1, 10.0)
        
        for i in range(1, num_points + 1):
            t = i / num_points
            
            # Bezier curve with random control points
            control_offset_x = np.random.normal(0, curve_intensity)
            control_offset_y = np.random.normal(0, curve_intensity)
            
            # Quadratic bezier interpolation
            x = (1-t)**2 * start_x + 2*(1-t)*t * (start_x + end_x)/2 + control_offset_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * (start_y + end_y)/2 + control_offset_y + t**2 * end_y
            
            # Add small random variations
            x += np.random.normal(0, 0.5)
            y += np.random.normal(0, 0.5)
            
            points.append((x, y))
        
        return points
    
    def should_add_pause(self) -> bool:
        """Determine if a natural pause should be added."""
        # Random pauses to simulate human hesitation/thinking
        return random.random() < 0.02  # 2% chance of pause
    
    def get_pause_duration(self) -> float:
        """Get duration for natural pause."""
        # Short pauses between 100-300ms
        return np.random.uniform(0.1, 0.3)
    
    def get_smoothing_factor(self, base_smoothing: float) -> float:
        """Add slight variation to smoothing factor."""
        # Vary smoothing by ±10% to simulate inconsistent human movement
        variation = np.random.uniform(-0.1, 0.1)
        smoothing = base_smoothing * (1 + variation)
        return max(0.0, min(1.0, smoothing))
    
    def simulate_hand_tremor(self, intensity: float = 0.3) -> Tuple[float, float]:
        """Simulate natural hand tremor/shake."""
        # Use sine waves with random frequency for natural tremor
        current_time = time.time()
        tremor_freq1 = 8 + random.uniform(-2, 2)  # 6-10 Hz
        tremor_freq2 = 12 + random.uniform(-3, 3)  # 9-15 Hz
        
        tremor_x = intensity * (
            math.sin(current_time * tremor_freq1 * 2 * math.pi) * 0.6 +
            math.sin(current_time * tremor_freq2 * 2 * math.pi) * 0.4
        )
        
        tremor_y = intensity * (
            math.cos(current_time * tremor_freq1 * 2 * math.pi) * 0.6 +
            math.cos(current_time * tremor_freq2 * 2 * math.pi) * 0.4
        )
        
        return tremor_x, tremor_y
    
    def update_fatigue(self):
        """Update fatigue factor based on usage time."""
        current_time = time.time()
        time_since_last = current_time - self.last_action_time
        
        # Gradual fatigue increase over time
        if time_since_last > 1800:  # 30 minutes
            self.fatigue_factor = min(1.3, self.fatigue_factor + 0.01)
        elif time_since_last > 300:  # 5 minutes - recovery
            self.fatigue_factor = max(1.0, self.fatigue_factor - 0.005)
        
        self.last_action_time = current_time
