MV_FLAG = 4096  # Multi-value flag

PT_UNSPECIFIED = 0
PT_NULL = 1
PT_I2 = 2
PT_LONG = 3
PT_R4 = 4
PT_DOUBLE = 5
PT_CURRENCY = 6
PT_APPTIME = 7
PT_ERROR = 10
PT_BOOLEAN = 11
PT_OBJECT = 13
PT_I8 = 20
PT_STRING8 = 30
PT_UNICODE = 31
PT_SYSTIME = 64
PT_CLSID = 72
PT_BINARY = 258

PT_SHORT = PT_I2
PT_I4 = PT_LONG
PT_FLOAT = PT_R4
PT_R8 = PT_DOUBLE
PT_LONGLONG = PT_I8

PT_MV_I2 = MV_FLAG | PT_I2
PT_MV_LONG = MV_FLAG | PT_LONG
PT_MV_R4 = MV_FLAG | PT_R4
PT_MV_DOUBLE = MV_FLAG | PT_DOUBLE
PT_MV_CURRENCY = MV_FLAG | PT_CURRENCY
PT_MV_APPTIME = MV_FLAG | PT_APPTIME
PT_MV_SYSTIME = MV_FLAG | PT_SYSTIME
PT_MV_STRING8 = MV_FLAG | PT_STRING8
PT_MV_BINARY = MV_FLAG | PT_BINARY
PT_MV_UNICODE = MV_FLAG | PT_UNICODE
PT_MV_CLSID = MV_FLAG | PT_CLSID
PT_MV_I8 = MV_FLAG | PT_I8

PT_MV_SHORT = PT_MV_I2
PT_MV_I4 = PT_MV_LONG
PT_MV_FLOAT = PT_MV_R4
PT_MV_R8 = PT_MV_DOUBLE
PT_MV_LONGLONG = PT_MV_I8

PT_TSTRING = PT_UNICODE  # ???
PT_MV_TSTRING = MV_FLAG | PT_UNICODE


PROP_TYPE_MASK = 65535  # Mask for Property type


def PROP_TYPE(ulPropTag):
    return ulPropTag & PROP_TYPE_MASK


def PROP_ID(ulPropTag):
    return ulPropTag >> 16


def PROP_TAG(ulPropType, ulPropID):
    return (ulPropID << 16) | (ulPropType)


PROP_ID_NULL = 0
PROP_ID_INVALID = 65535
PR_NULL = PROP_TAG(PT_NULL, PROP_ID_NULL)


PR_ACKNOWLEDGEMENT_MODE = PROP_TAG(PT_LONG, 1)
PR_ACKNOWLEDGEMENT_MODE = PROP_TAG(PT_LONG, 1)
PR_ALTERNATE_RECIPIENT_ALLOWED = PROP_TAG(PT_BOOLEAN, 2)
PR_AUTHORIZING_USERS = PROP_TAG(PT_BINARY, 3)
PR_AUTO_FORWARD_COMMENT = PROP_TAG(PT_TSTRING, 4)
PR_AUTO_FORWARD_COMMENT_W = PROP_TAG(PT_UNICODE, 4)
PR_AUTO_FORWARD_COMMENT_W = PROP_TAG(PT_UNICODE, 4)
PR_AUTO_FORWARD_COMMENT_A = PROP_TAG(PT_STRING8, 4)
PR_AUTO_FORWARDED = PROP_TAG(PT_BOOLEAN, 5)
PR_CONTENT_CONFIDENTIALITY_ALGORITHM_ID = PROP_TAG(PT_BINARY, 6)
PR_CONTENT_CORRELATOR = PROP_TAG(PT_BINARY, 7)
PR_CONTENT_IDENTIFIER = PROP_TAG(PT_TSTRING, 8)
PR_CONTENT_IDENTIFIER_W = PROP_TAG(PT_UNICODE, 8)
PR_CONTENT_IDENTIFIER_A = PROP_TAG(PT_STRING8, 8)
PR_CONTENT_LENGTH = PROP_TAG(PT_LONG, 9)
PR_CONTENT_RETURN_REQUESTED = PROP_TAG(PT_BOOLEAN, 10)
PR_CONVERSATION_KEY = PROP_TAG(PT_BINARY, 11)
PR_CONVERSION_EITS = PROP_TAG(PT_BINARY, 12)
PR_CONVERSION_WITH_LOSS_PROHIBITED = PROP_TAG(PT_BOOLEAN, 13)
PR_CONVERTED_EITS = PROP_TAG(PT_BINARY, 14)
PR_DEFERRED_DELIVERY_TIME = PROP_TAG(PT_SYSTIME, 15)
PR_DELIVER_TIME = PROP_TAG(PT_SYSTIME, 16)
PR_DISCARD_REASON = PROP_TAG(PT_LONG, 17)
PR_DISCLOSURE_OF_RECIPIENTS = PROP_TAG(PT_BOOLEAN, 18)
PR_DL_EXPANSION_HISTORY = PROP_TAG(PT_BINARY, 19)
PR_DL_EXPANSION_PROHIBITED = PROP_TAG(PT_BOOLEAN, 20)
PR_EXPIRY_TIME = PROP_TAG(PT_SYSTIME, 21)
PR_IMPLICIT_CONVERSION_PROHIBITED = PROP_TAG(PT_BOOLEAN, 22)
PR_IMPORTANCE = PROP_TAG(PT_LONG, 23)
PR_IPM_ID = PROP_TAG(PT_BINARY, 24)
PR_LATEST_DELIVERY_TIME = PROP_TAG(PT_SYSTIME, 25)
PR_MESSAGE_CLASS = PROP_TAG(PT_TSTRING, 26)
PR_MESSAGE_CLASS_W = PROP_TAG(PT_UNICODE, 26)
PR_MESSAGE_CLASS_A = PROP_TAG(PT_STRING8, 26)
PR_MESSAGE_DELIVERY_ID = PROP_TAG(PT_BINARY, 27)
PR_MESSAGE_SECURITY_LABEL = PROP_TAG(PT_BINARY, 30)
PR_OBSOLETED_IPMS = PROP_TAG(PT_BINARY, 31)
PR_ORIGINALLY_INTENDED_RECIPIENT_NAME = PROP_TAG(PT_BINARY, 32)
PR_ORIGINAL_EITS = PROP_TAG(PT_BINARY, 33)
PR_ORIGINATOR_CERTIFICATE = PROP_TAG(PT_BINARY, 34)
PR_ORIGINATOR_DELIVERY_REPORT_REQUESTED = PROP_TAG(PT_BOOLEAN, 35)
PR_ORIGINATOR_RETURN_ADDRESS = PROP_TAG(PT_BINARY, 36)
PR_PARENT_KEY = PROP_TAG(PT_BINARY, 37)
PR_PRIORITY = PROP_TAG(PT_LONG, 38)
PR_ORIGIN_CHECK = PROP_TAG(PT_BINARY, 39)
PR_PROOF_OF_SUBMISSION_REQUESTED = PROP_TAG(PT_BOOLEAN, 40)
PR_READ_RECEIPT_REQUESTED = PROP_TAG(PT_BOOLEAN, 41)
PR_RECEIPT_TIME = PROP_TAG(PT_SYSTIME, 42)
PR_RECIPIENT_REASSIGNMENT_PROHIBITED = PROP_TAG(PT_BOOLEAN, 43)
PR_REDIRECTION_HISTORY = PROP_TAG(PT_BINARY, 44)
PR_RELATED_IPMS = PROP_TAG(PT_BINARY, 45)
PR_ORIGINAL_SENSITIVITY = PROP_TAG(PT_LONG, 46)
PR_LANGUAGES = PROP_TAG(PT_TSTRING, 47)
PR_LANGUAGES_W = PROP_TAG(PT_UNICODE, 47)
PR_LANGUAGES_A = PROP_TAG(PT_STRING8, 47)
PR_REPLY_TIME = PROP_TAG(PT_SYSTIME, 48)
PR_REPORT_TAG = PROP_TAG(PT_BINARY, 49)
PR_REPORT_TIME = PROP_TAG(PT_SYSTIME, 50)
PR_RETURNED_IPM = PROP_TAG(PT_BOOLEAN, 51)
PR_SECURITY = PROP_TAG(PT_LONG, 52)
PR_INCOMPLETE_COPY = PROP_TAG(PT_BOOLEAN, 53)
PR_SENSITIVITY = PROP_TAG(PT_LONG, 54)
PR_SUBJECT = PROP_TAG(PT_TSTRING, 55)
PR_SUBJECT_W = PROP_TAG(PT_UNICODE, 55)
PR_SUBJECT_A = PROP_TAG(PT_STRING8, 55)
PR_SUBJECT_IPM = PROP_TAG(PT_BINARY, 56)
PR_CLIENT_SUBMIT_TIME = PROP_TAG(PT_SYSTIME, 57)
PR_REPORT_NAME = PROP_TAG(PT_TSTRING, 58)
PR_REPORT_NAME_W = PROP_TAG(PT_UNICODE, 58)
PR_REPORT_NAME_A = PROP_TAG(PT_STRING8, 58)
PR_SENT_REPRESENTING_SEARCH_KEY = PROP_TAG(PT_BINARY, 59)
PR_X400_CONTENT_TYPE = PROP_TAG(PT_BINARY, 60)
PR_SUBJECT_PREFIX = PROP_TAG(PT_TSTRING, 61)
PR_SUBJECT_PREFIX_W = PROP_TAG(PT_UNICODE, 61)
PR_SUBJECT_PREFIX_A = PROP_TAG(PT_STRING8, 61)
PR_NON_RECEIPT_REASON = PROP_TAG(PT_LONG, 62)
PR_RECEIVED_BY_ENTRYID = PROP_TAG(PT_BINARY, 63)
PR_RECEIVED_BY_NAME = PROP_TAG(PT_TSTRING, 64)
PR_RECEIVED_BY_NAME_W = PROP_TAG(PT_UNICODE, 64)
PR_RECEIVED_BY_NAME_A = PROP_TAG(PT_STRING8, 64)
PR_SENT_REPRESENTING_ENTRYID = PROP_TAG(PT_BINARY, 65)
PR_SENT_REPRESENTING_NAME = PROP_TAG(PT_TSTRING, 66)
PR_SENT_REPRESENTING_NAME_W = PROP_TAG(PT_UNICODE, 66)
PR_SENT_REPRESENTING_NAME_A = PROP_TAG(PT_STRING8, 66)
PR_RCVD_REPRESENTING_ENTRYID = PROP_TAG(PT_BINARY, 67)
PR_RCVD_REPRESENTING_NAME = PROP_TAG(PT_TSTRING, 68)
PR_RCVD_REPRESENTING_NAME_W = PROP_TAG(PT_UNICODE, 68)
PR_RCVD_REPRESENTING_NAME_A = PROP_TAG(PT_STRING8, 68)
PR_REPORT_ENTRYID = PROP_TAG(PT_BINARY, 69)
PR_READ_RECEIPT_ENTRYID = PROP_TAG(PT_BINARY, 70)
PR_MESSAGE_SUBMISSION_ID = PROP_TAG(PT_BINARY, 71)
PR_PROVIDER_SUBMIT_TIME = PROP_TAG(PT_SYSTIME, 72)
PR_ORIGINAL_SUBJECT = PROP_TAG(PT_TSTRING, 73)
PR_ORIGINAL_SUBJECT_W = PROP_TAG(PT_UNICODE, 73)
PR_ORIGINAL_SUBJECT_A = PROP_TAG(PT_STRING8, 73)
PR_DISC_VAL = PROP_TAG(PT_BOOLEAN, 74)
PR_ORIG_MESSAGE_CLASS = PROP_TAG(PT_TSTRING, 75)
PR_ORIG_MESSAGE_CLASS_W = PROP_TAG(PT_UNICODE, 75)
PR_ORIG_MESSAGE_CLASS_A = PROP_TAG(PT_STRING8, 75)
PR_ORIGINAL_AUTHOR_ENTRYID = PROP_TAG(PT_BINARY, 76)
PR_ORIGINAL_AUTHOR_NAME = PROP_TAG(PT_TSTRING, 77)
PR_ORIGINAL_AUTHOR_NAME_W = PROP_TAG(PT_UNICODE, 77)
PR_ORIGINAL_AUTHOR_NAME_A = PROP_TAG(PT_STRING8, 77)
PR_ORIGINAL_SUBMIT_TIME = PROP_TAG(PT_SYSTIME, 78)
PR_REPLY_RECIPIENT_ENTRIES = PROP_TAG(PT_BINARY, 79)
PR_REPLY_RECIPIENT_NAMES = PROP_TAG(PT_TSTRING, 80)
PR_REPLY_RECIPIENT_NAMES_W = PROP_TAG(PT_UNICODE, 80)
PR_REPLY_RECIPIENT_NAMES_A = PROP_TAG(PT_STRING8, 80)
PR_RECEIVED_BY_SEARCH_KEY = PROP_TAG(PT_BINARY, 81)
PR_RCVD_REPRESENTING_SEARCH_KEY = PROP_TAG(PT_BINARY, 82)
PR_READ_RECEIPT_SEARCH_KEY = PROP_TAG(PT_BINARY, 83)
PR_REPORT_SEARCH_KEY = PROP_TAG(PT_BINARY, 84)
PR_ORIGINAL_DELIVERY_TIME = PROP_TAG(PT_SYSTIME, 85)
PR_ORIGINAL_AUTHOR_SEARCH_KEY = PROP_TAG(PT_BINARY, 86)
PR_MESSAGE_TO_ME = PROP_TAG(PT_BOOLEAN, 87)
PR_MESSAGE_CC_ME = PROP_TAG(PT_BOOLEAN, 88)
PR_MESSAGE_RECIP_ME = PROP_TAG(PT_BOOLEAN, 89)
PR_ORIGINAL_SENDER_NAME = PROP_TAG(PT_TSTRING, 90)
PR_ORIGINAL_SENDER_NAME_W = PROP_TAG(PT_UNICODE, 90)
PR_ORIGINAL_SENDER_NAME_A = PROP_TAG(PT_STRING8, 90)
PR_ORIGINAL_SENDER_ENTRYID = PROP_TAG(PT_BINARY, 91)
PR_ORIGINAL_SENDER_SEARCH_KEY = PROP_TAG(PT_BINARY, 92)
PR_ORIGINAL_SENT_REPRESENTING_NAME = PROP_TAG(PT_TSTRING, 93)
PR_ORIGINAL_SENT_REPRESENTING_NAME_W = PROP_TAG(PT_UNICODE, 93)
PR_ORIGINAL_SENT_REPRESENTING_NAME_A = PROP_TAG(PT_STRING8, 93)
PR_ORIGINAL_SENT_REPRESENTING_ENTRYID = PROP_TAG(PT_BINARY, 94)
PR_ORIGINAL_SENT_REPRESENTING_SEARCH_KEY = PROP_TAG(PT_BINARY, 95)
PR_START_DATE = PROP_TAG(PT_SYSTIME, 96)
PR_END_DATE = PROP_TAG(PT_SYSTIME, 97)
PR_OWNER_APPT_ID = PROP_TAG(PT_LONG, 98)
PR_RESPONSE_REQUESTED = PROP_TAG(PT_BOOLEAN, 99)
PR_SENT_REPRESENTING_ADDRTYPE = PROP_TAG(PT_TSTRING, 100)
PR_SENT_REPRESENTING_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 100)
PR_SENT_REPRESENTING_ADDRTYPE_A = PROP_TAG(PT_STRING8, 100)
PR_SENT_REPRESENTING_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 101)
PR_SENT_REPRESENTING_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 101)
PR_SENT_REPRESENTING_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 101)
PR_ORIGINAL_SENDER_ADDRTYPE = PROP_TAG(PT_TSTRING, 102)
PR_ORIGINAL_SENDER_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 102)
PR_ORIGINAL_SENDER_ADDRTYPE_A = PROP_TAG(PT_STRING8, 102)
PR_ORIGINAL_SENDER_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 103)
PR_ORIGINAL_SENDER_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 103)
PR_ORIGINAL_SENDER_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 103)
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE = PROP_TAG(PT_TSTRING, 104)
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 104)
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE_A = PROP_TAG(PT_STRING8, 104)
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 105)
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 105)
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 105)
PR_CONVERSATION_TOPIC = PROP_TAG(PT_TSTRING, 112)
PR_CONVERSATION_TOPIC_W = PROP_TAG(PT_UNICODE, 112)
PR_CONVERSATION_TOPIC_A = PROP_TAG(PT_STRING8, 112)
PR_CONVERSATION_INDEX = PROP_TAG(PT_BINARY, 113)
PR_ORIGINAL_DISPLAY_BCC = PROP_TAG(PT_TSTRING, 114)
PR_ORIGINAL_DISPLAY_BCC_W = PROP_TAG(PT_UNICODE, 114)
PR_ORIGINAL_DISPLAY_BCC_A = PROP_TAG(PT_STRING8, 114)
PR_ORIGINAL_DISPLAY_CC = PROP_TAG(PT_TSTRING, 115)
PR_ORIGINAL_DISPLAY_CC_W = PROP_TAG(PT_UNICODE, 115)
PR_ORIGINAL_DISPLAY_CC_A = PROP_TAG(PT_STRING8, 115)
PR_ORIGINAL_DISPLAY_TO = PROP_TAG(PT_TSTRING, 116)
PR_ORIGINAL_DISPLAY_TO_W = PROP_TAG(PT_UNICODE, 116)
PR_ORIGINAL_DISPLAY_TO_A = PROP_TAG(PT_STRING8, 116)
PR_RECEIVED_BY_ADDRTYPE = PROP_TAG(PT_TSTRING, 117)
PR_RECEIVED_BY_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 117)
PR_RECEIVED_BY_ADDRTYPE_A = PROP_TAG(PT_STRING8, 117)
PR_RECEIVED_BY_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 118)
PR_RECEIVED_BY_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 118)
PR_RECEIVED_BY_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 118)
PR_RCVD_REPRESENTING_ADDRTYPE = PROP_TAG(PT_TSTRING, 119)
PR_RCVD_REPRESENTING_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 119)
PR_RCVD_REPRESENTING_ADDRTYPE_A = PROP_TAG(PT_STRING8, 119)
PR_RCVD_REPRESENTING_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 120)
PR_RCVD_REPRESENTING_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 120)
PR_RCVD_REPRESENTING_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 120)
PR_ORIGINAL_AUTHOR_ADDRTYPE = PROP_TAG(PT_TSTRING, 121)
PR_ORIGINAL_AUTHOR_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 121)
PR_ORIGINAL_AUTHOR_ADDRTYPE_A = PROP_TAG(PT_STRING8, 121)
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 122)
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 122)
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 122)
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE = PROP_TAG(PT_TSTRING, 123)
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 123)
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE_A = PROP_TAG(PT_STRING8, 123)
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 124)
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 124)
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 124)
PR_TRANSPORT_MESSAGE_HEADERS = PROP_TAG(PT_TSTRING, 125)
PR_TRANSPORT_MESSAGE_HEADERS_W = PROP_TAG(PT_UNICODE, 125)
PR_TRANSPORT_MESSAGE_HEADERS_A = PROP_TAG(PT_STRING8, 125)
PR_DELEGATION = PROP_TAG(PT_BINARY, 126)
PR_TNEF_CORRELATION_KEY = PROP_TAG(PT_BINARY, 127)
PR_BODY = PROP_TAG(PT_TSTRING, 4096)
PR_BODY_W = PROP_TAG(PT_UNICODE, 4096)
PR_BODY_A = PROP_TAG(PT_STRING8, 4096)
PR_BODY_HTML = PROP_TAG(PT_TSTRING, 4115)
PR_BODY_HTML_W = PROP_TAG(PT_UNICODE, 4115)
PR_BODY_HTML_A = PROP_TAG(PT_STRING8, 4115)
PR_REPORT_TEXT = PROP_TAG(PT_TSTRING, 4097)
PR_REPORT_TEXT_W = PROP_TAG(PT_UNICODE, 4097)
PR_REPORT_TEXT_A = PROP_TAG(PT_STRING8, 4097)
PR_ORIGINATOR_AND_DL_EXPANSION_HISTORY = PROP_TAG(PT_BINARY, 4098)
PR_REPORTING_DL_NAME = PROP_TAG(PT_BINARY, 4099)
PR_REPORTING_MTA_CERTIFICATE = PROP_TAG(PT_BINARY, 4100)
PR_RTF_SYNC_BODY_CRC = PROP_TAG(PT_LONG, 4102)
PR_RTF_SYNC_BODY_COUNT = PROP_TAG(PT_LONG, 4103)
PR_RTF_SYNC_BODY_TAG = PROP_TAG(PT_TSTRING, 4104)
PR_RTF_SYNC_BODY_TAG_W = PROP_TAG(PT_UNICODE, 4104)
PR_RTF_SYNC_BODY_TAG_A = PROP_TAG(PT_STRING8, 4104)
PR_RTF_COMPRESSED = PROP_TAG(PT_BINARY, 4105)
PR_RTF_SYNC_PREFIX_COUNT = PROP_TAG(PT_LONG, 4112)
PR_RTF_SYNC_TRAILING_COUNT = PROP_TAG(PT_LONG, 4113)
PR_ORIGINALLY_INTENDED_RECIP_ENTRYID = PROP_TAG(PT_BINARY, 4114)
PR_CONTENT_INTEGRITY_CHECK = PROP_TAG(PT_BINARY, 3072)
PR_EXPLICIT_CONVERSION = PROP_TAG(PT_LONG, 3073)
PR_IPM_RETURN_REQUESTED = PROP_TAG(PT_BOOLEAN, 3074)
PR_MESSAGE_TOKEN = PROP_TAG(PT_BINARY, 3075)
PR_NDR_REASON_CODE = PROP_TAG(PT_LONG, 3076)
PR_NDR_DIAG_CODE = PROP_TAG(PT_LONG, 3077)
PR_NON_RECEIPT_NOTIFICATION_REQUESTED = PROP_TAG(PT_BOOLEAN, 3078)
PR_DELIVERY_POINT = PROP_TAG(PT_LONG, 3079)
PR_ORIGINATOR_NON_DELIVERY_REPORT_REQUESTED = PROP_TAG(PT_BOOLEAN, 3080)
PR_ORIGINATOR_REQUESTED_ALTERNATE_RECIPIENT = PROP_TAG(PT_BINARY, 3081)
PR_PHYSICAL_DELIVERY_BUREAU_FAX_DELIVERY = PROP_TAG(PT_BOOLEAN, 3082)
PR_PHYSICAL_DELIVERY_MODE = PROP_TAG(PT_LONG, 3083)
PR_PHYSICAL_DELIVERY_REPORT_REQUEST = PROP_TAG(PT_LONG, 3084)
PR_PHYSICAL_FORWARDING_ADDRESS = PROP_TAG(PT_BINARY, 3085)
PR_PHYSICAL_FORWARDING_ADDRESS_REQUESTED = PROP_TAG(PT_BOOLEAN, 3086)
PR_PHYSICAL_FORWARDING_PROHIBITED = PROP_TAG(PT_BOOLEAN, 3087)
PR_PHYSICAL_RENDITION_ATTRIBUTES = PROP_TAG(PT_BINARY, 3088)
PR_PROOF_OF_DELIVERY = PROP_TAG(PT_BINARY, 3089)
PR_PROOF_OF_DELIVERY_REQUESTED = PROP_TAG(PT_BOOLEAN, 3090)
PR_RECIPIENT_CERTIFICATE = PROP_TAG(PT_BINARY, 3091)
PR_RECIPIENT_NUMBER_FOR_ADVICE = PROP_TAG(PT_TSTRING, 3092)
PR_RECIPIENT_NUMBER_FOR_ADVICE_W = PROP_TAG(PT_UNICODE, 3092)
PR_RECIPIENT_NUMBER_FOR_ADVICE_A = PROP_TAG(PT_STRING8, 3092)
PR_RECIPIENT_TYPE = PROP_TAG(PT_LONG, 3093)
PR_REGISTERED_MAIL_TYPE = PROP_TAG(PT_LONG, 3094)
PR_REPLY_REQUESTED = PROP_TAG(PT_BOOLEAN, 3095)
PR_REQUESTED_DELIVERY_METHOD = PROP_TAG(PT_LONG, 3096)
PR_SENDER_ENTRYID = PROP_TAG(PT_BINARY, 3097)
PR_SENDER_NAME = PROP_TAG(PT_TSTRING, 3098)
PR_SENDER_NAME_W = PROP_TAG(PT_UNICODE, 3098)
PR_SENDER_NAME_A = PROP_TAG(PT_STRING8, 3098)
PR_SUPPLEMENTARY_INFO = PROP_TAG(PT_TSTRING, 3099)
PR_SUPPLEMENTARY_INFO_W = PROP_TAG(PT_UNICODE, 3099)
PR_SUPPLEMENTARY_INFO_A = PROP_TAG(PT_STRING8, 3099)
PR_TYPE_OF_MTS_USER = PROP_TAG(PT_LONG, 3100)
PR_SENDER_SEARCH_KEY = PROP_TAG(PT_BINARY, 3101)
PR_SENDER_ADDRTYPE = PROP_TAG(PT_TSTRING, 3102)
PR_SENDER_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 3102)
PR_SENDER_ADDRTYPE_A = PROP_TAG(PT_STRING8, 3102)
PR_SENDER_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 3103)
PR_SENDER_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 3103)
PR_SENDER_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 3103)
PR_CURRENT_VERSION = PROP_TAG(PT_I8, 3584)
PR_DELETE_AFTER_SUBMIT = PROP_TAG(PT_BOOLEAN, 3585)
PR_DISPLAY_BCC = PROP_TAG(PT_TSTRING, 3586)
PR_DISPLAY_BCC_W = PROP_TAG(PT_UNICODE, 3586)
PR_DISPLAY_BCC_A = PROP_TAG(PT_STRING8, 3586)
PR_DISPLAY_CC = PROP_TAG(PT_TSTRING, 3587)
PR_DISPLAY_CC_W = PROP_TAG(PT_UNICODE, 3587)
PR_DISPLAY_CC_A = PROP_TAG(PT_STRING8, 3587)
PR_DISPLAY_TO = PROP_TAG(PT_TSTRING, 3588)
PR_DISPLAY_TO_W = PROP_TAG(PT_UNICODE, 3588)
PR_DISPLAY_TO_A = PROP_TAG(PT_STRING8, 3588)
PR_PARENT_DISPLAY = PROP_TAG(PT_TSTRING, 3589)
PR_PARENT_DISPLAY_W = PROP_TAG(PT_UNICODE, 3589)
PR_PARENT_DISPLAY_A = PROP_TAG(PT_STRING8, 3589)
PR_MESSAGE_DELIVERY_TIME = PROP_TAG(PT_SYSTIME, 3590)
PR_MESSAGE_FLAGS = PROP_TAG(PT_LONG, 3591)
PR_MESSAGE_SIZE = PROP_TAG(PT_LONG, 3592)
PR_PARENT_ENTRYID = PROP_TAG(PT_BINARY, 3593)
PR_SENTMAIL_ENTRYID = PROP_TAG(PT_BINARY, 3594)
PR_CORRELATE = PROP_TAG(PT_BOOLEAN, 3596)
PR_CORRELATE_MTSID = PROP_TAG(PT_BINARY, 3597)
PR_DISCRETE_VALUES = PROP_TAG(PT_BOOLEAN, 3598)
PR_RESPONSIBILITY = PROP_TAG(PT_BOOLEAN, 3599)
PR_SPOOLER_STATUS = PROP_TAG(PT_LONG, 3600)
PR_TRANSPORT_STATUS = PROP_TAG(PT_LONG, 3601)
PR_MESSAGE_RECIPIENTS = PROP_TAG(PT_OBJECT, 3602)
PR_MESSAGE_ATTACHMENTS = PROP_TAG(PT_OBJECT, 3603)
PR_SUBMIT_FLAGS = PROP_TAG(PT_LONG, 3604)
PR_RECIPIENT_STATUS = PROP_TAG(PT_LONG, 3605)
PR_TRANSPORT_KEY = PROP_TAG(PT_LONG, 3606)
PR_MSG_STATUS = PROP_TAG(PT_LONG, 3607)
PR_MESSAGE_DOWNLOAD_TIME = PROP_TAG(PT_LONG, 3608)
PR_CREATION_VERSION = PROP_TAG(PT_I8, 3609)
PR_MODIFY_VERSION = PROP_TAG(PT_I8, 3610)
PR_HASATTACH = PROP_TAG(PT_BOOLEAN, 3611)
PR_BODY_CRC = PROP_TAG(PT_LONG, 3612)
PR_NORMALIZED_SUBJECT = PROP_TAG(PT_TSTRING, 3613)
PR_NORMALIZED_SUBJECT_W = PROP_TAG(PT_UNICODE, 3613)
PR_NORMALIZED_SUBJECT_A = PROP_TAG(PT_STRING8, 3613)
PR_RTF_IN_SYNC = PROP_TAG(PT_BOOLEAN, 3615)
PR_ATTACH_SIZE = PROP_TAG(PT_LONG, 3616)
PR_ATTACH_NUM = PROP_TAG(PT_LONG, 3617)
PR_PREPROCESS = PROP_TAG(PT_BOOLEAN, 3618)
PR_ORIGINATING_MTA_CERTIFICATE = PROP_TAG(PT_BINARY, 3621)
PR_PROOF_OF_SUBMISSION = PROP_TAG(PT_BINARY, 3622)
PR_ENTRYID = PROP_TAG(PT_BINARY, 4095)
PR_OBJECT_TYPE = PROP_TAG(PT_LONG, 4094)
PR_ICON = PROP_TAG(PT_BINARY, 4093)
PR_MINI_ICON = PROP_TAG(PT_BINARY, 4092)
PR_STORE_ENTRYID = PROP_TAG(PT_BINARY, 4091)
PR_STORE_RECORD_KEY = PROP_TAG(PT_BINARY, 4090)
PR_RECORD_KEY = PROP_TAG(PT_BINARY, 4089)
PR_MAPPING_SIGNATURE = PROP_TAG(PT_BINARY, 4088)
PR_ACCESS_LEVEL = PROP_TAG(PT_LONG, 4087)
PR_INSTANCE_KEY = PROP_TAG(PT_BINARY, 4086)
PR_ROW_TYPE = PROP_TAG(PT_LONG, 4085)
PR_ACCESS = PROP_TAG(PT_LONG, 4084)
PR_ROWID = PROP_TAG(PT_LONG, 12288)
PR_DISPLAY_NAME = PROP_TAG(PT_TSTRING, 12289)
PR_DISPLAY_NAME_W = PROP_TAG(PT_UNICODE, 12289)
PR_DISPLAY_NAME_A = PROP_TAG(PT_STRING8, 12289)
PR_ADDRTYPE = PROP_TAG(PT_TSTRING, 12290)
PR_ADDRTYPE_W = PROP_TAG(PT_UNICODE, 12290)
PR_ADDRTYPE_A = PROP_TAG(PT_STRING8, 12290)
PR_EMAIL_ADDRESS = PROP_TAG(PT_TSTRING, 12291)
PR_EMAIL_ADDRESS_W = PROP_TAG(PT_UNICODE, 12291)
PR_EMAIL_ADDRESS_A = PROP_TAG(PT_STRING8, 12291)
PR_COMMENT = PROP_TAG(PT_TSTRING, 12292)
PR_COMMENT_W = PROP_TAG(PT_UNICODE, 12292)
PR_COMMENT_A = PROP_TAG(PT_STRING8, 12292)
PR_DEPTH = PROP_TAG(PT_LONG, 12293)
PR_PROVIDER_DISPLAY = PROP_TAG(PT_TSTRING, 12294)
PR_PROVIDER_DISPLAY_W = PROP_TAG(PT_UNICODE, 12294)
PR_PROVIDER_DISPLAY_A = PROP_TAG(PT_STRING8, 12294)
PR_CREATION_TIME = PROP_TAG(PT_SYSTIME, 12295)
PR_LAST_MODIFICATION_TIME = PROP_TAG(PT_SYSTIME, 12296)
PR_RESOURCE_FLAGS = PROP_TAG(PT_LONG, 12297)
PR_PROVIDER_DLL_NAME = PROP_TAG(PT_TSTRING, 12298)
PR_PROVIDER_DLL_NAME_W = PROP_TAG(PT_UNICODE, 12298)
PR_PROVIDER_DLL_NAME_A = PROP_TAG(PT_STRING8, 12298)
PR_SEARCH_KEY = PROP_TAG(PT_BINARY, 12299)
PR_PROVIDER_UID = PROP_TAG(PT_BINARY, 12300)
PR_PROVIDER_ORDINAL = PROP_TAG(PT_LONG, 12301)
PR_FORM_VERSION = PROP_TAG(PT_TSTRING, 13057)
PR_FORM_VERSION_W = PROP_TAG(PT_UNICODE, 13057)
PR_FORM_VERSION_A = PROP_TAG(PT_STRING8, 13057)
PR_FORM_CLSID = PROP_TAG(PT_CLSID, 13058)
PR_FORM_CONTACT_NAME = PROP_TAG(PT_TSTRING, 13059)
PR_FORM_CONTACT_NAME_W = PROP_TAG(PT_UNICODE, 13059)
PR_FORM_CONTACT_NAME_A = PROP_TAG(PT_STRING8, 13059)
PR_FORM_CATEGORY = PROP_TAG(PT_TSTRING, 13060)
PR_FORM_CATEGORY_W = PROP_TAG(PT_UNICODE, 13060)
PR_FORM_CATEGORY_A = PROP_TAG(PT_STRING8, 13060)
PR_FORM_CATEGORY_SUB = PROP_TAG(PT_TSTRING, 13061)
PR_FORM_CATEGORY_SUB_W = PROP_TAG(PT_UNICODE, 13061)
PR_FORM_CATEGORY_SUB_A = PROP_TAG(PT_STRING8, 13061)
PR_FORM_HOST_MAP = PROP_TAG(PT_MV_LONG, 13062)
PR_FORM_HIDDEN = PROP_TAG(PT_BOOLEAN, 13063)
PR_FORM_DESIGNER_NAME = PROP_TAG(PT_TSTRING, 13064)
PR_FORM_DESIGNER_NAME_W = PROP_TAG(PT_UNICODE, 13064)
PR_FORM_DESIGNER_NAME_A = PROP_TAG(PT_STRING8, 13064)
PR_FORM_DESIGNER_GUID = PROP_TAG(PT_CLSID, 13065)
PR_FORM_MESSAGE_BEHAVIOR = PROP_TAG(PT_LONG, 13066)
PR_DEFAULT_STORE = PROP_TAG(PT_BOOLEAN, 13312)
PR_STORE_SUPPORT_MASK = PROP_TAG(PT_LONG, 13325)
PR_STORE_STATE = PROP_TAG(PT_LONG, 13326)
PR_IPM_SUBTREE_SEARCH_KEY = PROP_TAG(PT_BINARY, 13328)
PR_IPM_OUTBOX_SEARCH_KEY = PROP_TAG(PT_BINARY, 13329)
PR_IPM_WASTEBASKET_SEARCH_KEY = PROP_TAG(PT_BINARY, 13330)
PR_IPM_SENTMAIL_SEARCH_KEY = PROP_TAG(PT_BINARY, 13331)
PR_MDB_PROVIDER = PROP_TAG(PT_BINARY, 13332)
PR_RECEIVE_FOLDER_SETTINGS = PROP_TAG(PT_OBJECT, 13333)
PR_VALID_FOLDER_MASK = PROP_TAG(PT_LONG, 13791)
PR_IPM_SUBTREE_ENTRYID = PROP_TAG(PT_BINARY, 13792)
PR_IPM_OUTBOX_ENTRYID = PROP_TAG(PT_BINARY, 13794)
PR_IPM_WASTEBASKET_ENTRYID = PROP_TAG(PT_BINARY, 13795)
PR_IPM_SENTMAIL_ENTRYID = PROP_TAG(PT_BINARY, 13796)
PR_VIEWS_ENTRYID = PROP_TAG(PT_BINARY, 13797)
PR_COMMON_VIEWS_ENTRYID = PROP_TAG(PT_BINARY, 13798)
PR_FINDER_ENTRYID = PROP_TAG(PT_BINARY, 13799)
PR_CONTAINER_FLAGS = PROP_TAG(PT_LONG, 13824)
PR_FOLDER_TYPE = PROP_TAG(PT_LONG, 13825)
PR_CONTENT_COUNT = PROP_TAG(PT_LONG, 13826)
PR_CONTENT_UNREAD = PROP_TAG(PT_LONG, 13827)
PR_CREATE_TEMPLATES = PROP_TAG(PT_OBJECT, 13828)
PR_DETAILS_TABLE = PROP_TAG(PT_OBJECT, 13829)
PR_SEARCH = PROP_TAG(PT_OBJECT, 13831)
PR_SELECTABLE = PROP_TAG(PT_BOOLEAN, 13833)
PR_SUBFOLDERS = PROP_TAG(PT_BOOLEAN, 13834)
PR_STATUS = PROP_TAG(PT_LONG, 13835)
PR_ANR = PROP_TAG(PT_TSTRING, 13836)
PR_ANR_W = PROP_TAG(PT_UNICODE, 13836)
PR_ANR_A = PROP_TAG(PT_STRING8, 13836)
PR_CONTENTS_SORT_ORDER = PROP_TAG(PT_MV_LONG, 13837)
PR_CONTAINER_HIERARCHY = PROP_TAG(PT_OBJECT, 13838)
PR_CONTAINER_CONTENTS = PROP_TAG(PT_OBJECT, 13839)
PR_FOLDER_ASSOCIATED_CONTENTS = PROP_TAG(PT_OBJECT, 13840)
PR_DEF_CREATE_DL = PROP_TAG(PT_BINARY, 13841)
PR_DEF_CREATE_MAILUSER = PROP_TAG(PT_BINARY, 13842)
PR_CONTAINER_CLASS = PROP_TAG(PT_TSTRING, 13843)
PR_CONTAINER_CLASS_W = PROP_TAG(PT_UNICODE, 13843)
PR_CONTAINER_CLASS_A = PROP_TAG(PT_STRING8, 13843)
PR_CONTAINER_MODIFY_VERSION = PROP_TAG(PT_I8, 13844)
PR_AB_PROVIDER_ID = PROP_TAG(PT_BINARY, 13845)
PR_DEFAULT_VIEW_ENTRYID = PROP_TAG(PT_BINARY, 13846)
PR_ASSOC_CONTENT_COUNT = PROP_TAG(PT_LONG, 13847)
PR_ATTACHMENT_X400_PARAMETERS = PROP_TAG(PT_BINARY, 14080)
PR_ATTACH_DATA_OBJ = PROP_TAG(PT_OBJECT, 14081)
PR_ATTACH_DATA_BIN = PROP_TAG(PT_BINARY, 14081)
PR_ATTACH_ENCODING = PROP_TAG(PT_BINARY, 14082)
PR_ATTACH_EXTENSION = PROP_TAG(PT_TSTRING, 14083)
PR_ATTACH_EXTENSION_W = PROP_TAG(PT_UNICODE, 14083)
PR_ATTACH_EXTENSION_A = PROP_TAG(PT_STRING8, 14083)
PR_ATTACH_FILENAME = PROP_TAG(PT_TSTRING, 14084)
PR_ATTACH_FILENAME_W = PROP_TAG(PT_UNICODE, 14084)
PR_ATTACH_FILENAME_A = PROP_TAG(PT_STRING8, 14084)
PR_ATTACH_METHOD = PROP_TAG(PT_LONG, 14085)
PR_ATTACH_LONG_FILENAME = PROP_TAG(PT_TSTRING, 14087)
PR_ATTACH_LONG_FILENAME_W = PROP_TAG(PT_UNICODE, 14087)
PR_ATTACH_LONG_FILENAME_A = PROP_TAG(PT_STRING8, 14087)
PR_ATTACH_PATHNAME = PROP_TAG(PT_TSTRING, 14088)
PR_ATTACH_PATHNAME_W = PROP_TAG(PT_UNICODE, 14088)
PR_ATTACH_PATHNAME_A = PROP_TAG(PT_STRING8, 14088)
PR_ATTACH_RENDERING = PROP_TAG(PT_BINARY, 14089)
PR_ATTACH_TAG = PROP_TAG(PT_BINARY, 14090)
PR_RENDERING_POSITION = PROP_TAG(PT_LONG, 14091)
PR_ATTACH_TRANSPORT_NAME = PROP_TAG(PT_TSTRING, 14092)
PR_ATTACH_TRANSPORT_NAME_W = PROP_TAG(PT_UNICODE, 14092)
PR_ATTACH_TRANSPORT_NAME_A = PROP_TAG(PT_STRING8, 14092)
PR_ATTACH_LONG_PATHNAME = PROP_TAG(PT_TSTRING, 14093)
PR_ATTACH_LONG_PATHNAME_W = PROP_TAG(PT_UNICODE, 14093)
PR_ATTACH_LONG_PATHNAME_A = PROP_TAG(PT_STRING8, 14093)
PR_ATTACH_MIME_TAG = PROP_TAG(PT_TSTRING, 14094)
PR_ATTACH_MIME_TAG_W = PROP_TAG(PT_UNICODE, 14094)
PR_ATTACH_MIME_TAG_A = PROP_TAG(PT_STRING8, 14094)
PR_ATTACH_ADDITIONAL_INFO = PROP_TAG(PT_BINARY, 14095)
PR_DISPLAY_TYPE = PROP_TAG(PT_LONG, 14592)
PR_TEMPLATEID = PROP_TAG(PT_BINARY, 14594)
PR_PRIMARY_CAPABILITY = PROP_TAG(PT_BINARY, 14596)
PR_7BIT_DISPLAY_NAME = PROP_TAG(PT_STRING8, 14847)
PR_ACCOUNT = PROP_TAG(PT_TSTRING, 14848)
PR_ACCOUNT_W = PROP_TAG(PT_UNICODE, 14848)
PR_ACCOUNT_A = PROP_TAG(PT_STRING8, 14848)
PR_ALTERNATE_RECIPIENT = PROP_TAG(PT_BINARY, 14849)
PR_CALLBACK_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14850)
PR_CALLBACK_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14850)
PR_CALLBACK_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14850)
PR_CONVERSION_PROHIBITED = PROP_TAG(PT_BOOLEAN, 14851)
PR_DISCLOSE_RECIPIENTS = PROP_TAG(PT_BOOLEAN, 14852)
PR_GENERATION = PROP_TAG(PT_TSTRING, 14853)
PR_GENERATION_W = PROP_TAG(PT_UNICODE, 14853)
PR_GENERATION_A = PROP_TAG(PT_STRING8, 14853)
PR_GIVEN_NAME = PROP_TAG(PT_TSTRING, 14854)
PR_GIVEN_NAME_W = PROP_TAG(PT_UNICODE, 14854)
PR_GIVEN_NAME_A = PROP_TAG(PT_STRING8, 14854)
PR_GOVERNMENT_ID_NUMBER = PROP_TAG(PT_TSTRING, 14855)
PR_GOVERNMENT_ID_NUMBER_W = PROP_TAG(PT_UNICODE, 14855)
PR_GOVERNMENT_ID_NUMBER_A = PROP_TAG(PT_STRING8, 14855)
PR_BUSINESS_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14856)
PR_BUSINESS_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14856)
PR_BUSINESS_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14856)
PR_OFFICE_TELEPHONE_NUMBER = PR_BUSINESS_TELEPHONE_NUMBER
PR_OFFICE_TELEPHONE_NUMBER_W = PR_BUSINESS_TELEPHONE_NUMBER_W
PR_OFFICE_TELEPHONE_NUMBER_A = PR_BUSINESS_TELEPHONE_NUMBER_A
PR_HOME_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14857)
PR_HOME_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14857)
PR_HOME_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14857)
PR_INITIALS = PROP_TAG(PT_TSTRING, 14858)
PR_INITIALS_W = PROP_TAG(PT_UNICODE, 14858)
PR_INITIALS_A = PROP_TAG(PT_STRING8, 14858)
PR_KEYWORD = PROP_TAG(PT_TSTRING, 14859)
PR_KEYWORD_W = PROP_TAG(PT_UNICODE, 14859)
PR_KEYWORD_A = PROP_TAG(PT_STRING8, 14859)
PR_LANGUAGE = PROP_TAG(PT_TSTRING, 14860)
PR_LANGUAGE_W = PROP_TAG(PT_UNICODE, 14860)
PR_LANGUAGE_A = PROP_TAG(PT_STRING8, 14860)
PR_LOCATION = PROP_TAG(PT_TSTRING, 14861)
PR_LOCATION_W = PROP_TAG(PT_UNICODE, 14861)
PR_LOCATION_A = PROP_TAG(PT_STRING8, 14861)
PR_MAIL_PERMISSION = PROP_TAG(PT_BOOLEAN, 14862)
PR_MHS_COMMON_NAME = PROP_TAG(PT_TSTRING, 14863)
PR_MHS_COMMON_NAME_W = PROP_TAG(PT_UNICODE, 14863)
PR_MHS_COMMON_NAME_A = PROP_TAG(PT_STRING8, 14863)
PR_ORGANIZATIONAL_ID_NUMBER = PROP_TAG(PT_TSTRING, 14864)
PR_ORGANIZATIONAL_ID_NUMBER_W = PROP_TAG(PT_UNICODE, 14864)
PR_ORGANIZATIONAL_ID_NUMBER_A = PROP_TAG(PT_STRING8, 14864)
PR_SURNAME = PROP_TAG(PT_TSTRING, 14865)
PR_SURNAME_W = PROP_TAG(PT_UNICODE, 14865)
PR_SURNAME_A = PROP_TAG(PT_STRING8, 14865)
PR_ORIGINAL_ENTRYID = PROP_TAG(PT_BINARY, 14866)
PR_ORIGINAL_DISPLAY_NAME = PROP_TAG(PT_TSTRING, 14867)
PR_ORIGINAL_DISPLAY_NAME_W = PROP_TAG(PT_UNICODE, 14867)
PR_ORIGINAL_DISPLAY_NAME_A = PROP_TAG(PT_STRING8, 14867)
PR_ORIGINAL_SEARCH_KEY = PROP_TAG(PT_BINARY, 14868)
PR_POSTAL_ADDRESS = PROP_TAG(PT_TSTRING, 14869)
PR_POSTAL_ADDRESS_W = PROP_TAG(PT_UNICODE, 14869)
PR_POSTAL_ADDRESS_A = PROP_TAG(PT_STRING8, 14869)
PR_COMPANY_NAME = PROP_TAG(PT_TSTRING, 14870)
PR_COMPANY_NAME_W = PROP_TAG(PT_UNICODE, 14870)
PR_COMPANY_NAME_A = PROP_TAG(PT_STRING8, 14870)
PR_TITLE = PROP_TAG(PT_TSTRING, 14871)
PR_TITLE_W = PROP_TAG(PT_UNICODE, 14871)
PR_TITLE_A = PROP_TAG(PT_STRING8, 14871)
PR_DEPARTMENT_NAME = PROP_TAG(PT_TSTRING, 14872)
PR_DEPARTMENT_NAME_W = PROP_TAG(PT_UNICODE, 14872)
PR_DEPARTMENT_NAME_A = PROP_TAG(PT_STRING8, 14872)
PR_OFFICE_LOCATION = PROP_TAG(PT_TSTRING, 14873)
PR_OFFICE_LOCATION_W = PROP_TAG(PT_UNICODE, 14873)
PR_OFFICE_LOCATION_A = PROP_TAG(PT_STRING8, 14873)
PR_PRIMARY_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14874)
PR_PRIMARY_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14874)
PR_PRIMARY_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14874)
PR_BUSINESS2_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14875)
PR_BUSINESS2_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14875)
PR_BUSINESS2_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14875)
PR_OFFICE2_TELEPHONE_NUMBER = PR_BUSINESS2_TELEPHONE_NUMBER
PR_OFFICE2_TELEPHONE_NUMBER_W = PR_BUSINESS2_TELEPHONE_NUMBER_W
PR_OFFICE2_TELEPHONE_NUMBER_A = PR_BUSINESS2_TELEPHONE_NUMBER_A
PR_MOBILE_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14876)
PR_MOBILE_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14876)
PR_MOBILE_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14876)
PR_CELLULAR_TELEPHONE_NUMBER = PR_MOBILE_TELEPHONE_NUMBER
PR_CELLULAR_TELEPHONE_NUMBER_W = PR_MOBILE_TELEPHONE_NUMBER_W
PR_CELLULAR_TELEPHONE_NUMBER_A = PR_MOBILE_TELEPHONE_NUMBER_A
PR_RADIO_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14877)
PR_RADIO_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14877)
PR_RADIO_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14877)
PR_CAR_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14878)
PR_CAR_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14878)
PR_CAR_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14878)
PR_OTHER_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14879)
PR_OTHER_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14879)
PR_OTHER_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14879)
PR_TRANSMITABLE_DISPLAY_NAME = PROP_TAG(PT_TSTRING, 14880)
PR_TRANSMITABLE_DISPLAY_NAME_W = PROP_TAG(PT_UNICODE, 14880)
PR_TRANSMITABLE_DISPLAY_NAME_A = PROP_TAG(PT_STRING8, 14880)
PR_PAGER_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14881)
PR_PAGER_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14881)
PR_PAGER_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14881)
PR_BEEPER_TELEPHONE_NUMBER = PR_PAGER_TELEPHONE_NUMBER
PR_BEEPER_TELEPHONE_NUMBER_W = PR_PAGER_TELEPHONE_NUMBER_W
PR_BEEPER_TELEPHONE_NUMBER_A = PR_PAGER_TELEPHONE_NUMBER_A
PR_USER_CERTIFICATE = PROP_TAG(PT_BINARY, 14882)
PR_PRIMARY_FAX_NUMBER = PROP_TAG(PT_TSTRING, 14883)
PR_PRIMARY_FAX_NUMBER_W = PROP_TAG(PT_UNICODE, 14883)
PR_PRIMARY_FAX_NUMBER_A = PROP_TAG(PT_STRING8, 14883)
PR_BUSINESS_FAX_NUMBER = PROP_TAG(PT_TSTRING, 14884)
PR_BUSINESS_FAX_NUMBER_W = PROP_TAG(PT_UNICODE, 14884)
PR_BUSINESS_FAX_NUMBER_A = PROP_TAG(PT_STRING8, 14884)
PR_HOME_FAX_NUMBER = PROP_TAG(PT_TSTRING, 14885)
PR_HOME_FAX_NUMBER_W = PROP_TAG(PT_UNICODE, 14885)
PR_HOME_FAX_NUMBER_A = PROP_TAG(PT_STRING8, 14885)
PR_COUNTRY = PROP_TAG(PT_TSTRING, 14886)
PR_COUNTRY_W = PROP_TAG(PT_UNICODE, 14886)
PR_COUNTRY_A = PROP_TAG(PT_STRING8, 14886)
PR_BUSINESS_ADDRESS_COUNTRY = PR_COUNTRY
PR_BUSINESS_ADDRESS_COUNTRY_W = PR_COUNTRY_W
PR_BUSINESS_ADDRESS_COUNTRY_A = PR_COUNTRY_A
PR_LOCALITY = PROP_TAG(PT_TSTRING, 14887)
PR_LOCALITY_W = PROP_TAG(PT_UNICODE, 14887)
PR_LOCALITY_A = PROP_TAG(PT_STRING8, 14887)
PR_BUSINESS_ADDRESS_CITY = PR_LOCALITY
PR_BUSINESS_ADDRESS_CITY_W = PR_LOCALITY_W
PR_BUSINESS_ADDRESS_CITY_A = PR_LOCALITY_A
PR_STATE_OR_PROVINCE = PROP_TAG(PT_TSTRING, 14888)
PR_STATE_OR_PROVINCE_W = PROP_TAG(PT_UNICODE, 14888)
PR_STATE_OR_PROVINCE_A = PROP_TAG(PT_STRING8, 14888)
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE = PR_STATE_OR_PROVINCE
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE_W = PR_STATE_OR_PROVINCE_W
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE_A = PR_STATE_OR_PROVINCE_A
PR_STREET_ADDRESS = PROP_TAG(PT_TSTRING, 14889)
PR_STREET_ADDRESS_W = PROP_TAG(PT_UNICODE, 14889)
PR_STREET_ADDRESS_A = PROP_TAG(PT_STRING8, 14889)
PR_BUSINESS_ADDRESS_STREET = PR_STREET_ADDRESS
PR_BUSINESS_ADDRESS_STREET_W = PR_STREET_ADDRESS_W
PR_BUSINESS_ADDRESS_STREET_A = PR_STREET_ADDRESS_A
PR_POSTAL_CODE = PROP_TAG(PT_TSTRING, 14890)
PR_POSTAL_CODE_W = PROP_TAG(PT_UNICODE, 14890)
PR_POSTAL_CODE_A = PROP_TAG(PT_STRING8, 14890)
PR_BUSINESS_ADDRESS_POSTAL_CODE = PR_POSTAL_CODE
PR_BUSINESS_ADDRESS_POSTAL_CODE_W = PR_POSTAL_CODE_W
PR_BUSINESS_ADDRESS_POSTAL_CODE_A = PR_POSTAL_CODE_A
PR_POST_OFFICE_BOX = PROP_TAG(PT_TSTRING, 14891)
PR_POST_OFFICE_BOX_W = PROP_TAG(PT_UNICODE, 14891)
PR_POST_OFFICE_BOX_A = PROP_TAG(PT_STRING8, 14891)
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX = PR_POST_OFFICE_BOX
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX_W = PR_POST_OFFICE_BOX_W
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX_A = PR_POST_OFFICE_BOX_A
PR_TELEX_NUMBER = PROP_TAG(PT_TSTRING, 14892)
PR_TELEX_NUMBER_W = PROP_TAG(PT_UNICODE, 14892)
PR_TELEX_NUMBER_A = PROP_TAG(PT_STRING8, 14892)
PR_ISDN_NUMBER = PROP_TAG(PT_TSTRING, 14893)
PR_ISDN_NUMBER_W = PROP_TAG(PT_UNICODE, 14893)
PR_ISDN_NUMBER_A = PROP_TAG(PT_STRING8, 14893)
PR_ASSISTANT_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14894)
PR_ASSISTANT_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14894)
PR_ASSISTANT_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14894)
PR_HOME2_TELEPHONE_NUMBER = PROP_TAG(PT_TSTRING, 14895)
PR_HOME2_TELEPHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14895)
PR_HOME2_TELEPHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14895)
PR_ASSISTANT = PROP_TAG(PT_TSTRING, 14896)
PR_ASSISTANT_W = PROP_TAG(PT_UNICODE, 14896)
PR_ASSISTANT_A = PROP_TAG(PT_STRING8, 14896)
PR_SEND_RICH_INFO = PROP_TAG(PT_BOOLEAN, 14912)
PR_WEDDING_ANNIVERSARY = PROP_TAG(PT_SYSTIME, 14913)
PR_BIRTHDAY = PROP_TAG(PT_SYSTIME, 14914)
PR_HOBBIES = PROP_TAG(PT_TSTRING, 14915)
PR_HOBBIES_W = PROP_TAG(PT_UNICODE, 14915)
PR_HOBBIES_A = PROP_TAG(PT_STRING8, 14915)
PR_MIDDLE_NAME = PROP_TAG(PT_TSTRING, 14916)
PR_MIDDLE_NAME_W = PROP_TAG(PT_UNICODE, 14916)
PR_MIDDLE_NAME_A = PROP_TAG(PT_STRING8, 14916)
PR_DISPLAY_NAME_PREFIX = PROP_TAG(PT_TSTRING, 14917)
PR_DISPLAY_NAME_PREFIX_W = PROP_TAG(PT_UNICODE, 14917)
PR_DISPLAY_NAME_PREFIX_A = PROP_TAG(PT_STRING8, 14917)
PR_PROFESSION = PROP_TAG(PT_TSTRING, 14918)
PR_PROFESSION_W = PROP_TAG(PT_UNICODE, 14918)
PR_PROFESSION_A = PROP_TAG(PT_STRING8, 14918)
PR_PREFERRED_BY_NAME = PROP_TAG(PT_TSTRING, 14919)
PR_PREFERRED_BY_NAME_W = PROP_TAG(PT_UNICODE, 14919)
PR_PREFERRED_BY_NAME_A = PROP_TAG(PT_STRING8, 14919)
PR_SPOUSE_NAME = PROP_TAG(PT_TSTRING, 14920)
PR_SPOUSE_NAME_W = PROP_TAG(PT_UNICODE, 14920)
PR_SPOUSE_NAME_A = PROP_TAG(PT_STRING8, 14920)
PR_COMPUTER_NETWORK_NAME = PROP_TAG(PT_TSTRING, 14921)
PR_COMPUTER_NETWORK_NAME_W = PROP_TAG(PT_UNICODE, 14921)
PR_COMPUTER_NETWORK_NAME_A = PROP_TAG(PT_STRING8, 14921)
PR_CUSTOMER_ID = PROP_TAG(PT_TSTRING, 14922)
PR_CUSTOMER_ID_W = PROP_TAG(PT_UNICODE, 14922)
PR_CUSTOMER_ID_A = PROP_TAG(PT_STRING8, 14922)
PR_TTYTDD_PHONE_NUMBER = PROP_TAG(PT_TSTRING, 14923)
PR_TTYTDD_PHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14923)
PR_TTYTDD_PHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14923)
PR_FTP_SITE = PROP_TAG(PT_TSTRING, 14924)
PR_FTP_SITE_W = PROP_TAG(PT_UNICODE, 14924)
PR_FTP_SITE_A = PROP_TAG(PT_STRING8, 14924)
PR_GENDER = PROP_TAG(PT_SHORT, 14925)
PR_MANAGER_NAME = PROP_TAG(PT_TSTRING, 14926)
PR_MANAGER_NAME_W = PROP_TAG(PT_UNICODE, 14926)
PR_MANAGER_NAME_A = PROP_TAG(PT_STRING8, 14926)
PR_NICKNAME = PROP_TAG(PT_TSTRING, 14927)
PR_NICKNAME_W = PROP_TAG(PT_UNICODE, 14927)
PR_NICKNAME_A = PROP_TAG(PT_STRING8, 14927)
PR_PERSONAL_HOME_PAGE = PROP_TAG(PT_TSTRING, 14928)
PR_PERSONAL_HOME_PAGE_W = PROP_TAG(PT_UNICODE, 14928)
PR_PERSONAL_HOME_PAGE_A = PROP_TAG(PT_STRING8, 14928)
PR_BUSINESS_HOME_PAGE = PROP_TAG(PT_TSTRING, 14929)
PR_BUSINESS_HOME_PAGE_W = PROP_TAG(PT_UNICODE, 14929)
PR_BUSINESS_HOME_PAGE_A = PROP_TAG(PT_STRING8, 14929)
PR_CONTACT_VERSION = PROP_TAG(PT_CLSID, 14930)
PR_CONTACT_ENTRYIDS = PROP_TAG(PT_MV_BINARY, 14931)
PR_CONTACT_ADDRTYPES = PROP_TAG(PT_MV_TSTRING, 14932)
PR_CONTACT_ADDRTYPES_W = PROP_TAG(PT_MV_UNICODE, 14932)
PR_CONTACT_ADDRTYPES_A = PROP_TAG(PT_MV_STRING8, 14932)
PR_CONTACT_DEFAULT_ADDRESS_INDEX = PROP_TAG(PT_LONG, 14933)
PR_CONTACT_EMAIL_ADDRESSES = PROP_TAG(PT_MV_TSTRING, 14934)
PR_CONTACT_EMAIL_ADDRESSES_W = PROP_TAG(PT_MV_UNICODE, 14934)
PR_CONTACT_EMAIL_ADDRESSES_A = PROP_TAG(PT_MV_STRING8, 14934)
PR_COMPANY_MAIN_PHONE_NUMBER = PROP_TAG(PT_TSTRING, 14935)
PR_COMPANY_MAIN_PHONE_NUMBER_W = PROP_TAG(PT_UNICODE, 14935)
PR_COMPANY_MAIN_PHONE_NUMBER_A = PROP_TAG(PT_STRING8, 14935)
PR_CHILDRENS_NAMES = PROP_TAG(PT_MV_TSTRING, 14936)
PR_CHILDRENS_NAMES_W = PROP_TAG(PT_MV_UNICODE, 14936)
PR_CHILDRENS_NAMES_A = PROP_TAG(PT_MV_STRING8, 14936)
PR_HOME_ADDRESS_CITY = PROP_TAG(PT_TSTRING, 14937)
PR_HOME_ADDRESS_CITY_W = PROP_TAG(PT_UNICODE, 14937)
PR_HOME_ADDRESS_CITY_A = PROP_TAG(PT_STRING8, 14937)
PR_HOME_ADDRESS_COUNTRY = PROP_TAG(PT_TSTRING, 14938)
PR_HOME_ADDRESS_COUNTRY_W = PROP_TAG(PT_UNICODE, 14938)
PR_HOME_ADDRESS_COUNTRY_A = PROP_TAG(PT_STRING8, 14938)
PR_HOME_ADDRESS_POSTAL_CODE = PROP_TAG(PT_TSTRING, 14939)
PR_HOME_ADDRESS_POSTAL_CODE_W = PROP_TAG(PT_UNICODE, 14939)
PR_HOME_ADDRESS_POSTAL_CODE_A = PROP_TAG(PT_STRING8, 14939)
PR_HOME_ADDRESS_STATE_OR_PROVINCE = PROP_TAG(PT_TSTRING, 14940)
PR_HOME_ADDRESS_STATE_OR_PROVINCE_W = PROP_TAG(PT_UNICODE, 14940)
PR_HOME_ADDRESS_STATE_OR_PROVINCE_A = PROP_TAG(PT_STRING8, 14940)
PR_HOME_ADDRESS_STREET = PROP_TAG(PT_TSTRING, 14941)
PR_HOME_ADDRESS_STREET_W = PROP_TAG(PT_UNICODE, 14941)
PR_HOME_ADDRESS_STREET_A = PROP_TAG(PT_STRING8, 14941)
PR_HOME_ADDRESS_POST_OFFICE_BOX = PROP_TAG(PT_TSTRING, 14942)
PR_HOME_ADDRESS_POST_OFFICE_BOX_W = PROP_TAG(PT_UNICODE, 14942)
PR_HOME_ADDRESS_POST_OFFICE_BOX_A = PROP_TAG(PT_STRING8, 14942)
PR_OTHER_ADDRESS_CITY = PROP_TAG(PT_TSTRING, 14943)
PR_OTHER_ADDRESS_CITY_W = PROP_TAG(PT_UNICODE, 14943)
PR_OTHER_ADDRESS_CITY_A = PROP_TAG(PT_STRING8, 14943)
PR_OTHER_ADDRESS_COUNTRY = PROP_TAG(PT_TSTRING, 14944)
PR_OTHER_ADDRESS_COUNTRY_W = PROP_TAG(PT_UNICODE, 14944)
PR_OTHER_ADDRESS_COUNTRY_A = PROP_TAG(PT_STRING8, 14944)
PR_OTHER_ADDRESS_POSTAL_CODE = PROP_TAG(PT_TSTRING, 14945)
PR_OTHER_ADDRESS_POSTAL_CODE_W = PROP_TAG(PT_UNICODE, 14945)
PR_OTHER_ADDRESS_POSTAL_CODE_A = PROP_TAG(PT_STRING8, 14945)
PR_OTHER_ADDRESS_STATE_OR_PROVINCE = PROP_TAG(PT_TSTRING, 14946)
PR_OTHER_ADDRESS_STATE_OR_PROVINCE_W = PROP_TAG(PT_UNICODE, 14946)
PR_OTHER_ADDRESS_STATE_OR_PROVINCE_A = PROP_TAG(PT_STRING8, 14946)
PR_OTHER_ADDRESS_STREET = PROP_TAG(PT_TSTRING, 14947)
PR_OTHER_ADDRESS_STREET_W = PROP_TAG(PT_UNICODE, 14947)
PR_OTHER_ADDRESS_STREET_A = PROP_TAG(PT_STRING8, 14947)
PR_OTHER_ADDRESS_POST_OFFICE_BOX = PROP_TAG(PT_TSTRING, 14948)
PR_OTHER_ADDRESS_POST_OFFICE_BOX_W = PROP_TAG(PT_UNICODE, 14948)
PR_OTHER_ADDRESS_POST_OFFICE_BOX_A = PROP_TAG(PT_STRING8, 14948)
PR_STORE_PROVIDERS = PROP_TAG(PT_BINARY, 15616)
PR_AB_PROVIDERS = PROP_TAG(PT_BINARY, 15617)
PR_TRANSPORT_PROVIDERS = PROP_TAG(PT_BINARY, 15618)
PR_DEFAULT_PROFILE = PROP_TAG(PT_BOOLEAN, 15620)
PR_AB_SEARCH_PATH = PROP_TAG(PT_MV_BINARY, 15621)
PR_AB_DEFAULT_DIR = PROP_TAG(PT_BINARY, 15622)
PR_AB_DEFAULT_PAB = PROP_TAG(PT_BINARY, 15623)
PR_FILTERING_HOOKS = PROP_TAG(PT_BINARY, 15624)
PR_SERVICE_NAME = PROP_TAG(PT_TSTRING, 15625)
PR_SERVICE_NAME_W = PROP_TAG(PT_UNICODE, 15625)
PR_SERVICE_NAME_A = PROP_TAG(PT_STRING8, 15625)
PR_SERVICE_DLL_NAME = PROP_TAG(PT_TSTRING, 15626)
PR_SERVICE_DLL_NAME_W = PROP_TAG(PT_UNICODE, 15626)
PR_SERVICE_DLL_NAME_A = PROP_TAG(PT_STRING8, 15626)
PR_SERVICE_ENTRY_NAME = PROP_TAG(PT_STRING8, 15627)
PR_SERVICE_UID = PROP_TAG(PT_BINARY, 15628)
PR_SERVICE_EXTRA_UIDS = PROP_TAG(PT_BINARY, 15629)
PR_SERVICES = PROP_TAG(PT_BINARY, 15630)
PR_SERVICE_SUPPORT_FILES = PROP_TAG(PT_MV_TSTRING, 15631)
PR_SERVICE_SUPPORT_FILES_W = PROP_TAG(PT_MV_UNICODE, 15631)
PR_SERVICE_SUPPORT_FILES_A = PROP_TAG(PT_MV_STRING8, 15631)
PR_SERVICE_DELETE_FILES = PROP_TAG(PT_MV_TSTRING, 15632)
PR_SERVICE_DELETE_FILES_W = PROP_TAG(PT_MV_UNICODE, 15632)
PR_SERVICE_DELETE_FILES_A = PROP_TAG(PT_MV_STRING8, 15632)
PR_AB_SEARCH_PATH_UPDATE = PROP_TAG(PT_BINARY, 15633)
PR_PROFILE_NAME = PROP_TAG(PT_TSTRING, 15634)
PR_PROFILE_NAME_A = PROP_TAG(PT_STRING8, 15634)
PR_PROFILE_NAME_W = PROP_TAG(PT_UNICODE, 15634)
PR_IDENTITY_DISPLAY = PROP_TAG(PT_TSTRING, 15872)
PR_IDENTITY_DISPLAY_W = PROP_TAG(PT_UNICODE, 15872)
PR_IDENTITY_DISPLAY_A = PROP_TAG(PT_STRING8, 15872)
PR_IDENTITY_ENTRYID = PROP_TAG(PT_BINARY, 15873)
PR_RESOURCE_METHODS = PROP_TAG(PT_LONG, 15874)
PR_RESOURCE_TYPE = PROP_TAG(PT_LONG, 15875)
PR_STATUS_CODE = PROP_TAG(PT_LONG, 15876)
PR_IDENTITY_SEARCH_KEY = PROP_TAG(PT_BINARY, 15877)
PR_OWN_STORE_ENTRYID = PROP_TAG(PT_BINARY, 15878)
PR_RESOURCE_PATH = PROP_TAG(PT_TSTRING, 15879)
PR_RESOURCE_PATH_W = PROP_TAG(PT_UNICODE, 15879)
PR_RESOURCE_PATH_A = PROP_TAG(PT_STRING8, 15879)
PR_STATUS_STRING = PROP_TAG(PT_TSTRING, 15880)
PR_STATUS_STRING_W = PROP_TAG(PT_UNICODE, 15880)
PR_STATUS_STRING_A = PROP_TAG(PT_STRING8, 15880)
PR_X400_DEFERRED_DELIVERY_CANCEL = PROP_TAG(PT_BOOLEAN, 15881)
PR_HEADER_FOLDER_ENTRYID = PROP_TAG(PT_BINARY, 15882)
PR_REMOTE_PROGRESS = PROP_TAG(PT_LONG, 15883)
PR_REMOTE_PROGRESS_TEXT = PROP_TAG(PT_TSTRING, 15884)
PR_REMOTE_PROGRESS_TEXT_W = PROP_TAG(PT_UNICODE, 15884)
PR_REMOTE_PROGRESS_TEXT_A = PROP_TAG(PT_STRING8, 15884)
PR_REMOTE_VALIDATE_OK = PROP_TAG(PT_BOOLEAN, 15885)
PR_CONTROL_FLAGS = PROP_TAG(PT_LONG, 16128)
PR_CONTROL_STRUCTURE = PROP_TAG(PT_BINARY, 16129)
PR_CONTROL_TYPE = PROP_TAG(PT_LONG, 16130)
PR_DELTAX = PROP_TAG(PT_LONG, 16131)
PR_DELTAY = PROP_TAG(PT_LONG, 16132)
PR_XPOS = PROP_TAG(PT_LONG, 16133)
PR_YPOS = PROP_TAG(PT_LONG, 16134)
PR_CONTROL_ID = PROP_TAG(PT_BINARY, 16135)
PR_INITIAL_DETAILS_PANE = PROP_TAG(PT_LONG, 16136)

PROP_ID_SECURE_MIN = 26608
PROP_ID_SECURE_MAX = 26623

# From EDKMDB.H
pidExchangeXmitReservedMin = 16352
pidExchangeNonXmitReservedMin = 26080
pidProfileMin = 26112
pidStoreMin = 26136
pidFolderMin = 26168
pidMessageReadOnlyMin = 26176
pidMessageWriteableMin = 26200
pidAttachReadOnlyMin = 26220
pidSpecialMin = 26224
pidAdminMin = 26256
pidSecureProfileMin = PROP_ID_SECURE_MIN

PR_PROFILE_VERSION = PROP_TAG(PT_LONG, pidProfileMin + 0)
PR_PROFILE_CONFIG_FLAGS = PROP_TAG(PT_LONG, pidProfileMin + 1)
PR_PROFILE_HOME_SERVER = PROP_TAG(PT_STRING8, pidProfileMin + 2)
PR_PROFILE_HOME_SERVER_DN = PROP_TAG(PT_STRING8, pidProfileMin + 18)
PR_PROFILE_HOME_SERVER_ADDRS = PROP_TAG(PT_MV_STRING8, pidProfileMin + 19)
PR_PROFILE_USER = PROP_TAG(PT_STRING8, pidProfileMin + 3)
PR_PROFILE_CONNECT_FLAGS = PROP_TAG(PT_LONG, pidProfileMin + 4)
PR_PROFILE_TRANSPORT_FLAGS = PROP_TAG(PT_LONG, pidProfileMin + 5)
PR_PROFILE_UI_STATE = PROP_TAG(PT_LONG, pidProfileMin + 6)
PR_PROFILE_UNRESOLVED_NAME = PROP_TAG(PT_STRING8, pidProfileMin + 7)
PR_PROFILE_UNRESOLVED_SERVER = PROP_TAG(PT_STRING8, pidProfileMin + 8)
PR_PROFILE_BINDING_ORDER = PROP_TAG(PT_STRING8, pidProfileMin + 9)
PR_PROFILE_MAX_RESTRICT = PROP_TAG(PT_LONG, pidProfileMin + 13)
PR_PROFILE_AB_FILES_PATH = PROP_TAG(PT_STRING8, pidProfileMin + 14)
PR_PROFILE_OFFLINE_STORE_PATH = PROP_TAG(PT_STRING8, pidProfileMin + 16)
PR_PROFILE_OFFLINE_INFO = PROP_TAG(PT_BINARY, pidProfileMin + 17)
PR_PROFILE_ADDR_INFO = PROP_TAG(PT_BINARY, pidSpecialMin + 23)
PR_PROFILE_OPTIONS_DATA = PROP_TAG(PT_BINARY, pidSpecialMin + 25)
PR_PROFILE_SECURE_MAILBOX = PROP_TAG(PT_BINARY, pidSecureProfileMin + 0)
PR_DISABLE_WINSOCK = PROP_TAG(PT_LONG, pidProfileMin + 24)
PR_OST_ENCRYPTION = PROP_TAG(PT_LONG, 26370)
PR_PROFILE_OPEN_FLAGS = PROP_TAG(PT_LONG, pidProfileMin + 9)
PR_PROFILE_TYPE = PROP_TAG(PT_LONG, pidProfileMin + 10)
PR_PROFILE_MAILBOX = PROP_TAG(PT_STRING8, pidProfileMin + 11)
PR_PROFILE_SERVER = PROP_TAG(PT_STRING8, pidProfileMin + 12)
PR_PROFILE_SERVER_DN = PROP_TAG(PT_STRING8, pidProfileMin + 20)
PR_PROFILE_FAVFLD_DISPLAY_NAME = PROP_TAG(PT_STRING8, pidProfileMin + 15)
PR_PROFILE_FAVFLD_COMMENT = PROP_TAG(PT_STRING8, pidProfileMin + 21)
PR_PROFILE_ALLPUB_DISPLAY_NAME = PROP_TAG(PT_STRING8, pidProfileMin + 22)
PR_PROFILE_ALLPUB_COMMENT = PROP_TAG(PT_STRING8, pidProfileMin + 23)

OSTF_NO_ENCRYPTION = -2147483648
OSTF_COMPRESSABLE_ENCRYPTION = 1073741824
OSTF_BEST_ENCRYPTION = 536870912


PR_NON_IPM_SUBTREE_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 8)
PR_EFORMS_REGISTRY_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 9)
PR_SPLUS_FREE_BUSY_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 10)
PR_OFFLINE_ADDRBOOK_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 11)
PR_EFORMS_FOR_LOCALE_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 12)
PR_FREE_BUSY_FOR_LOCAL_SITE_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 13)
PR_ADDRBOOK_FOR_LOCAL_SITE_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 14)
PR_OFFLINE_MESSAGE_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 15)
PR_IPM_FAVORITES_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 24)
PR_IPM_PUBLIC_FOLDERS_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 25)
PR_GW_MTSIN_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 16)
PR_GW_MTSOUT_ENTRYID = PROP_TAG(PT_BINARY, pidStoreMin + 17)
PR_TRANSFER_ENABLED = PROP_TAG(PT_BOOLEAN, pidStoreMin + 18)
PR_TEST_LINE_SPEED = PROP_TAG(PT_BINARY, pidStoreMin + 19)
PR_HIERARCHY_SYNCHRONIZER = PROP_TAG(PT_OBJECT, pidStoreMin + 20)
PR_CONTENTS_SYNCHRONIZER = PROP_TAG(PT_OBJECT, pidStoreMin + 21)
PR_COLLECTOR = PROP_TAG(PT_OBJECT, pidStoreMin + 22)
PR_FAST_TRANSFER = PROP_TAG(PT_OBJECT, pidStoreMin + 23)
PR_STORE_OFFLINE = PROP_TAG(PT_BOOLEAN, pidStoreMin + 26)
PR_IN_TRANSIT = PROP_TAG(PT_BOOLEAN, pidStoreMin)
PR_REPLICATION_STYLE = PROP_TAG(PT_LONG, pidAdminMin)
PR_REPLICATION_SCHEDULE = PROP_TAG(PT_BINARY, pidAdminMin + 1)
PR_REPLICATION_MESSAGE_PRIORITY = PROP_TAG(PT_LONG, pidAdminMin + 2)
PR_OVERALL_MSG_AGE_LIMIT = PROP_TAG(PT_LONG, pidAdminMin + 3)
PR_REPLICATION_ALWAYS_INTERVAL = PROP_TAG(PT_LONG, pidAdminMin + 4)
PR_REPLICATION_MSG_SIZE = PROP_TAG(PT_LONG, pidAdminMin + 5)
STYLE_ALWAYS_INTERVAL_DEFAULT = 15
REPLICATION_MESSAGE_SIZE_LIMIT_DEFAULT = 100
STYLE_NEVER = 0
STYLE_NORMAL = 1
STYLE_ALWAYS = 2
STYLE_DEFAULT = -1
PR_SOURCE_KEY = PROP_TAG(PT_BINARY, pidExchangeNonXmitReservedMin + 0)
PR_PARENT_SOURCE_KEY = PROP_TAG(PT_BINARY, pidExchangeNonXmitReservedMin + 1)
PR_CHANGE_KEY = PROP_TAG(PT_BINARY, pidExchangeNonXmitReservedMin + 2)
PR_PREDECESSOR_CHANGE_LIST = PROP_TAG(PT_BINARY, pidExchangeNonXmitReservedMin + 3)
PR_FOLDER_CHILD_COUNT = PROP_TAG(PT_LONG, pidFolderMin)
PR_RIGHTS = PROP_TAG(PT_LONG, pidFolderMin + 1)
PR_ACL_TABLE = PROP_TAG(PT_OBJECT, pidExchangeXmitReservedMin)
PR_RULES_TABLE = PROP_TAG(PT_OBJECT, pidExchangeXmitReservedMin + 1)
PR_HAS_RULES = PROP_TAG(PT_BOOLEAN, pidFolderMin + 2)
PR_ADDRESS_BOOK_ENTRYID = PROP_TAG(PT_BINARY, pidFolderMin + 3)
PR_ACL_DATA = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin)
PR_RULES_DATA = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 1)
PR_FOLDER_DESIGN_FLAGS = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 2)
PR_DESIGN_IN_PROGRESS = PROP_TAG(PT_BOOLEAN, pidExchangeXmitReservedMin + 4)
PR_SECURE_ORIGINATION = PROP_TAG(PT_BOOLEAN, pidExchangeXmitReservedMin + 5)
PR_PUBLISH_IN_ADDRESS_BOOK = PROP_TAG(PT_BOOLEAN, pidExchangeXmitReservedMin + 6)
PR_RESOLVE_METHOD = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 7)
PR_ADDRESS_BOOK_DISPLAY_NAME = PROP_TAG(PT_TSTRING, pidExchangeXmitReservedMin + 8)
PR_EFORMS_LOCALE_ID = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 9)
PR_REPLICA_LIST = PROP_TAG(PT_BINARY, pidAdminMin + 8)
PR_OVERALL_AGE_LIMIT = PROP_TAG(PT_LONG, pidAdminMin + 9)
RESOLVE_METHOD_DEFAULT = 0
RESOLVE_METHOD_LAST_WRITER_WINS = 1
RESOLVE_METHOD_NO_CONFLICT_NOTIFICATION = 2
PR_PUBLIC_FOLDER_ENTRYID = PROP_TAG(PT_BINARY, pidFolderMin + 4)
PR_HAS_NAMED_PROPERTIES = PROP_TAG(PT_BOOLEAN, pidMessageReadOnlyMin + 10)
PR_CREATOR_NAME = PROP_TAG(PT_TSTRING, pidExchangeXmitReservedMin + 24)
PR_CREATOR_ENTRYID = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 25)
PR_LAST_MODIFIER_NAME = PROP_TAG(PT_TSTRING, pidExchangeXmitReservedMin + 26)
PR_LAST_MODIFIER_ENTRYID = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 27)
PR_HAS_DAMS = PROP_TAG(PT_BOOLEAN, pidExchangeXmitReservedMin + 10)
PR_RULE_TRIGGER_HISTORY = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 18)
PR_MOVE_TO_STORE_ENTRYID = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 19)
PR_MOVE_TO_FOLDER_ENTRYID = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 20)
PR_REPLICA_SERVER = PROP_TAG(PT_TSTRING, pidMessageReadOnlyMin + 4)
PR_DEFERRED_SEND_NUMBER = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 11)
PR_DEFERRED_SEND_UNITS = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 12)
PR_EXPIRY_NUMBER = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 13)
PR_EXPIRY_UNITS = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 14)
PR_DEFERRED_SEND_TIME = PROP_TAG(PT_SYSTIME, pidExchangeXmitReservedMin + 15)
PR_GW_ADMIN_OPERATIONS = PROP_TAG(PT_LONG, pidMessageWriteableMin)
PR_P1_CONTENT = PROP_TAG(PT_BINARY, 4352)
PR_P1_CONTENT_TYPE = PROP_TAG(PT_BINARY, 4353)
PR_CLIENT_ACTIONS = PROP_TAG(PT_BINARY, pidMessageReadOnlyMin + 5)
PR_DAM_ORIGINAL_ENTRYID = PROP_TAG(PT_BINARY, pidMessageReadOnlyMin + 6)
PR_DAM_BACK_PATCHED = PROP_TAG(PT_BOOLEAN, pidMessageReadOnlyMin + 7)
PR_RULE_ERROR = PROP_TAG(PT_LONG, pidMessageReadOnlyMin + 8)
PR_RULE_ACTION_TYPE = PROP_TAG(PT_LONG, pidMessageReadOnlyMin + 9)
PR_RULE_ACTION_NUMBER = PROP_TAG(PT_LONG, pidMessageReadOnlyMin + 16)
PR_RULE_FOLDER_ENTRYID = PROP_TAG(PT_BINARY, pidMessageReadOnlyMin + 17)
PR_CONFLICT_ENTRYID = PROP_TAG(PT_BINARY, pidExchangeXmitReservedMin + 16)
PR_MESSAGE_LOCALE_ID = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 17)
PR_STORAGE_QUOTA_LIMIT = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 21)
PR_EXCESS_STORAGE_USED = PROP_TAG(PT_LONG, pidExchangeXmitReservedMin + 22)
PR_SVR_GENERATING_QUOTA_MSG = PROP_TAG(PT_TSTRING, pidExchangeXmitReservedMin + 23)
PR_DELEGATED_BY_RULE = PROP_TAG(PT_BOOLEAN, pidExchangeXmitReservedMin + 3)
MSGSTATUS_IN_CONFLICT = 2048
PR_IN_CONFLICT = PROP_TAG(PT_BOOLEAN, pidAttachReadOnlyMin)
PR_LONGTERM_ENTRYID_FROM_TABLE = PROP_TAG(PT_BINARY, pidSpecialMin)
PR_ORIGINATOR_NAME = PROP_TAG(PT_TSTRING, pidMessageWriteableMin + 3)
PR_ORIGINATOR_ADDR = PROP_TAG(PT_TSTRING, pidMessageWriteableMin + 4)
PR_ORIGINATOR_ADDRTYPE = PROP_TAG(PT_TSTRING, pidMessageWriteableMin + 5)
PR_ORIGINATOR_ENTRYID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 6)
PR_ARRIVAL_TIME = PROP_TAG(PT_SYSTIME, pidMessageWriteableMin + 7)
PR_TRACE_INFO = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 8)
PR_INTERNAL_TRACE_INFO = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 18)
PR_SUBJECT_TRACE_INFO = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 9)
PR_RECIPIENT_NUMBER = PROP_TAG(PT_LONG, pidMessageWriteableMin + 10)
PR_MTS_SUBJECT_ID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 11)
PR_REPORT_DESTINATION_NAME = PROP_TAG(PT_TSTRING, pidMessageWriteableMin + 12)
PR_REPORT_DESTINATION_ENTRYID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 13)
PR_CONTENT_SEARCH_KEY = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 14)
PR_FOREIGN_ID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 15)
PR_FOREIGN_REPORT_ID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 16)
PR_FOREIGN_SUBJECT_ID = PROP_TAG(PT_BINARY, pidMessageWriteableMin + 17)
PR_MTS_ID = PR_MESSAGE_SUBMISSION_ID
PR_MTS_REPORT_ID = PR_MESSAGE_SUBMISSION_ID

PR_FOLDER_FLAGS = PROP_TAG(PT_LONG, pidAdminMin + 24)
PR_LAST_ACCESS_TIME = PROP_TAG(PT_SYSTIME, pidAdminMin + 25)
PR_RESTRICTION_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 26)
PR_CATEG_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 27)
PR_CACHED_COLUMN_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 28)
PR_NORMAL_MSG_W_ATTACH_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 29)
PR_ASSOC_MSG_W_ATTACH_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 30)
PR_RECIPIENT_ON_NORMAL_MSG_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 31)
PR_RECIPIENT_ON_ASSOC_MSG_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 32)
PR_ATTACH_ON_NORMAL_MSG_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 33)
PR_ATTACH_ON_ASSOC_MSG_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 34)
PR_NORMAL_MESSAGE_SIZE = PROP_TAG(PT_LONG, pidAdminMin + 35)
PR_NORMAL_MESSAGE_SIZE_EXTENDED = PROP_TAG(PT_I8, pidAdminMin + 35)
PR_ASSOC_MESSAGE_SIZE = PROP_TAG(PT_LONG, pidAdminMin + 36)
PR_ASSOC_MESSAGE_SIZE_EXTENDED = PROP_TAG(PT_I8, pidAdminMin + 36)
PR_FOLDER_PATHNAME = PROP_TAG(PT_TSTRING, pidAdminMin + 37)
PR_OWNER_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 38)
PR_CONTACT_COUNT = PROP_TAG(PT_LONG, pidAdminMin + 39)

PR_MESSAGE_SIZE_EXTENDED = PROP_TAG(PT_I8, PROP_ID(PR_MESSAGE_SIZE))

PR_USERFIELDS = PROP_TAG(PT_BINARY, 0x36E3)

# IExchangeManageStoreEx::CreateStoreEntryID2
PR_FORCE_USE_ENTRYID_SERVER = PROP_TAG(PT_BOOLEAN, 0x7CFE)
PR_PROFILE_MDB_DN = PROP_TAG(PT_STRING8, 0x7CFF)

# MSPST.h
PST_EXTERN_PROPID_BASE = 0x6700
PR_PST_PATH = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 0)
PR_PST_PATH_W = PROP_TAG(PT_UNICODE, PST_EXTERN_PROPID_BASE + 0)
PR_PST_PATH_A = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 0)
PR_PST_REMEMBER_PW = PROP_TAG(PT_BOOLEAN, PST_EXTERN_PROPID_BASE + 1)
PR_PST_ENCRYPTION = PROP_TAG(PT_LONG, PST_EXTERN_PROPID_BASE + 2)
PR_PST_PW_SZ_OLD = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 3)
PR_PST_PW_SZ_OLD_W = PROP_TAG(PT_UNICODE, PST_EXTERN_PROPID_BASE + 3)
PR_PST_PW_SZ_OLD_A = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 3)
PR_PST_PW_SZ_NEW = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 4)
PR_PST_PW_SZ_NEW_W = PROP_TAG(PT_UNICODE, PST_EXTERN_PROPID_BASE + 4)
PR_PST_PW_SZ_NEW_A = PROP_TAG(PT_STRING8, PST_EXTERN_PROPID_BASE + 4)
