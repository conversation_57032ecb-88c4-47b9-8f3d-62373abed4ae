"""
    Serial Input Handler - Microcontroller-based input simulation
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import threading
import numpy as np
try:
    import serial
except ImportError:
    print("Warning: PySerial library not available")
    serial = None

from .base_microcontroller_input import BaseMicrocontrollerInputHandler
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from behavior_randomizer import BehaviorRandomizer


class SerialInputHandler(BaseMicrocontrollerInputHandler):
    """Serial communication based input handler for microcontroller devices."""
    
    def __init__(self, config):
        """Initialize the Serial input handler."""
        super().__init__(config)
        self.behavior_randomizer = BehaviorRandomizer()
        self.device = None
        self.connect_to_device()
        
    def connect_to_device(self) -> None:
        """Connect to the microcontroller device via serial."""
        if not serial:
            print("PySerial library not available")
            return
            
        try:
            port = self.config.serial_port
            self.device = serial.Serial(port, 9600, timeout=1)
            time.sleep(2)  # Wait for device initialization
            print(f"Connected to serial device on {port}")
        except Exception as e:
            print(f"Failed to connect to serial device: {e}")
            self.device = None
            
    def send_command(self, command: str) -> None:
        """Send command to the microcontroller."""
        if not self.device:
            return
            
        try:
            self.device.write(command.encode())
            self.device.flush()
            
            # Wait for acknowledgment
            response = self.device.readline().decode().strip()
            if response != "a":
                print(f"Unexpected device response: {response}")
                
        except Exception as e:
            print(f"Error sending command to device: {e}")
            
    def get_response(self) -> str:
        """Get response from the microcontroller."""
        if not self.device:
            return ""
            
        try:
            return self.device.readline().decode().strip()
        except Exception as e:
            print(f"Error reading device response: {e}")
            return ""
            
    def send_cursor_movement(self, x: int, y: int) -> None:
        """Send cursor movement command to microcontroller."""
        if not self.device:
            return
            
        try:
            # Add human-like movement variations
            adjusted_x, adjusted_y = self.behavior_randomizer.add_movement_jitter(x, y)
            
            # Send movement command
            command = self.get_movement_command(int(adjusted_x), int(adjusted_y))
            self.send_command(command)
            
            if self.config.debug:
                print(f'(Serial) Movement: ({int(adjusted_x)}, {int(adjusted_y)})')
                
        except Exception as e:
            print(f"Error in serial cursor movement: {e}")
            
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """Send action input command to microcontroller."""
        if not self.device:
            return
            
        try:
            # Apply pre-action delay
            if delay_before_action > 0:
                time.sleep(delay_before_action)
                
            self.last_action_time = time.time()
            
            # Send action command (microcontroller handles timing)
            self.send_command(self.action_command)
            
            if self.config.debug:
                print('(Serial) Action sent')
                
            # Small delay to prevent command flooding
            time.sleep(0.025)
            
        except Exception as e:
            print(f"Error in serial action input: {e}")
            
    def close_connection(self) -> None:
        """Close the serial connection."""
        if self.device:
            try:
                self.device.close()
                self.device = None
                print("Serial connection closed")
            except Exception as e:
                print(f"Error closing serial connection: {e}")
