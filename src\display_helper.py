"""
    DisplayHelper - Advanced Visual Processing System
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import random
import math
import numpy as np
import gc
from typing import Optional, <PERSON>ple

from movement_controller import MovementController
from input_manager import get_input_implementation
from visual_processor import VisualProcessor
from system_manager import SystemManager
from behavior_randomizer import BehaviorRandomizer


class DisplayHelper:
    """Advanced visual processing and input management system."""
    
    def __init__(self):
        """Initialize the display helper system."""
        self.is_running = False
        self.system_manager = None
        self.movement_controller = None
        self.input_manager = None
        self.visual_processor = None
        self.behavior_randomizer = BehaviorRandomizer()
        
        # Performance optimization
        self.last_cleanup_time = time.time()
        self.cleanup_interval = 30.0  # Cleanup every 30 seconds

        # Performance monitoring
        self.frame_times = []
        self.max_frame_history = 100
        self.performance_warning_threshold = 50.0  # ms

        # Error recovery
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        self.error_recovery_delay = 1.0
        
    def start_processing(self):
        """Start the main processing loop."""
        self._print_system_info()
        
        while True:  # Main loop with configuration hot-reload support
            try:
                # Initialize system components
                if not self._initialize_components():
                    break
                    
                print('System Active')
                self.is_running = True
                
                # Main processing loop
                self._main_processing_loop()
                
            except Exception as e:
                print(f"Error in processing loop: {e}")
                self._handle_processing_error(e)
                self._cleanup_resources()
                time.sleep(self.error_recovery_delay)
                
    def _initialize_components(self) -> bool:
        """Initialize all system components."""
        try:
            # Record initialization start time
            start_time = time.time()
            
            self.system_manager = SystemManager()
            config = self.system_manager.get_configuration()
            
            self.movement_controller = MovementController(config)
            self.input_manager = get_input_implementation(config)
            self.visual_processor = VisualProcessor(config)
            
            return True
            
        except Exception as e:
            print(f"Failed to initialize components: {e}")
            return False
            
    def _main_processing_loop(self):
        """Main processing loop for visual analysis and input handling."""
        frame_start_time = time.time()
        
        while self.is_running:
            try:
                # Calculate frame timing
                current_time = time.time()
                delta_time = current_time - frame_start_time
                frame_start_time = current_time
                
                # Check for configuration reload
                if self.system_manager.check_system_controls():
                    break  # Exit loop to reload configuration
                
                # Process visual input and handle responses
                self._process_frame(delta_time)

                # Record frame processing time
                frame_time_ms = (time.time() - frame_start_time) * 1000
                self._record_frame_time(frame_time_ms)

                # Reset error count on successful frame
                self._reset_error_count()

                # Periodic cleanup for performance
                self._periodic_cleanup(current_time)

                # Frame rate control
                self._control_frame_rate(frame_start_time)
                
            except Exception as e:
                print(f"Error in frame processing: {e}")
                time.sleep(0.001)  # Brief pause to prevent tight error loop
                
        # Cleanup when exiting loop
        self._cleanup_resources()
        print('Reloading Configuration')
        
    def _process_frame(self, delta_time: float):
        """Process a single frame of visual input."""
        config = self.system_manager.get_configuration()
        
        # Determine if processing is needed
        processing_needed = (
            self.system_manager.get_enhancement_state() or 
            self.system_manager.get_auto_action_state() or
            (config.debug and config.debug_always_on)
        )
        
        if processing_needed:
            # Get visual target information
            target_info, action_trigger = self.visual_processor.analyze_frame(
                self.movement_controller.get_offset_compensation()
            )
            
            # Handle automatic actions
            if self.system_manager.get_auto_action_state() and action_trigger:
                self._handle_automatic_action(config)
            
            # Calculate movement adjustments
            self.movement_controller.calculate_movement(
                self.system_manager.get_enhancement_state(), 
                target_info
            )
        
        # Handle rapid input mode
        if self.system_manager.get_rapid_input_state():
            self.input_manager.perform_action()
        
        # Apply movement compensation
        self.movement_controller.apply_compensation(
            self.system_manager.get_compensation_state(), 
            delta_time
        )
        
        # Execute calculated movements
        movement_x, movement_y = self.movement_controller.get_movement_delta()
        if movement_x != 0 or movement_y != 0:
            self.input_manager.move_cursor(movement_x, movement_y)
        
        # Reset movement deltas
        self.movement_controller.reset_movement_delta()
        
    def _handle_automatic_action(self, config):
        """Handle automatic action with human-like timing."""
        if config.action_delay != 0:
            # Calculate action delay with randomization
            delay = self.behavior_randomizer.get_action_delay(
                config.action_delay, 
                config.action_randomization
            )
        else:
            delay = 0
            
        self.input_manager.perform_action(delay)
        
    def _control_frame_rate(self, frame_start_time: float):
        """Control the frame rate to maintain consistent timing."""
        config = self.system_manager.get_configuration()
        frame_time = (time.time() - frame_start_time) * 1000
        
        if frame_time < config.min_frame_time:
            sleep_time = (config.min_frame_time - frame_time) / 1000
            time.sleep(sleep_time)
            
    def _periodic_cleanup(self, current_time: float):
        """Perform periodic cleanup for memory management."""
        if current_time - self.last_cleanup_time > self.cleanup_interval:
            gc.collect()  # Force garbage collection
            self.last_cleanup_time = current_time
            self._check_performance_health()

    def _check_performance_health(self):
        """Check system performance and log warnings if needed."""
        if len(self.frame_times) > 10:
            avg_frame_time = sum(self.frame_times[-10:]) / 10
            if avg_frame_time > self.performance_warning_threshold:
                print(f"Performance warning: Average frame time {avg_frame_time:.1f}ms")

    def _record_frame_time(self, frame_time_ms: float):
        """Record frame processing time for performance monitoring."""
        self.frame_times.append(frame_time_ms)
        if len(self.frame_times) > self.max_frame_history:
            self.frame_times.pop(0)

    def _handle_processing_error(self, error: Exception):
        """Handle processing errors with recovery logic."""
        self.consecutive_errors += 1

        if self.consecutive_errors >= self.max_consecutive_errors:
            print(f"Too many consecutive errors ({self.consecutive_errors}). Increasing recovery delay.")
            self.error_recovery_delay = min(5.0, self.error_recovery_delay * 1.5)

        # Reset error count on successful operation (called elsewhere)

    def _reset_error_count(self):
        """Reset error count after successful operation."""
        if self.consecutive_errors > 0:
            self.consecutive_errors = 0
            self.error_recovery_delay = 1.0
            
    def _cleanup_resources(self):
        """Clean up system resources."""
        try:
            if hasattr(self, 'system_manager') and self.system_manager:
                del self.system_manager
            if hasattr(self, 'movement_controller') and self.movement_controller:
                del self.movement_controller
            if hasattr(self, 'input_manager') and self.input_manager:
                del self.input_manager
            if hasattr(self, 'visual_processor') and self.visual_processor:
                del self.visual_processor
                
            # Force garbage collection
            gc.collect()
            
        except Exception as e:
            print(f"Error during cleanup: {e}")
            
    def _print_system_info(self):
        """Print system information and license."""
        print('DisplayHelper System  Copyright (C) 2025  Development Team')
        print('This program comes with ABSOLUTELY NO WARRANTY.')
        print('This is free software, and you are welcome to redistribute it under certain conditions.')
        print('For details see <LICENSE.txt>.')
