"""
    Visual Processor - Advanced screen analysis and target detection
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON><PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import cv2
import mss
import numpy as np
import win32api
import time
import random
from typing import Optional, Tuple
from behavior_randomizer import BehaviorRandomizer


class VisualProcessor:
    """Advanced visual processing and target detection system."""
    
    def __init__(self, config):
        """Initialize the visual processor."""
        self.config = config
        self.behavior_randomizer = BehaviorRandomizer()
        
        # Initialize screen capture
        self.screen_capture = mss.mss()
        
        # Setup capture region
        self._setup_capture_region()
        
        # Setup color detection parameters
        self._setup_color_detection()
        
        # Performance optimization
        self.frame_skip_counter = 0
        self.adaptive_quality = True
        self.last_detection_time = time.time()
        
        # Detection history for stability
        self.detection_history = []
        self.max_history_length = 3
        
    def _setup_capture_region(self) -> None:
        """Setup the screen capture region."""
        if self.config.auto_detect_resolution:
            self.screen_width = win32api.GetSystemMetrics(0)
            self.screen_height = win32api.GetSystemMetrics(1)
        else:
            self.screen_width = self.config.resolution_x
            self.screen_height = self.config.resolution_y
            
        # Calculate dynamic capture region
        self.capture_region = {
            "top": int((self.screen_height - self.config.capture_region_y) / 2),
            "left": int((self.screen_width - self.config.capture_region_x) / 2),
            "width": self.config.capture_region_x,
            "height": self.config.capture_region_y,
        }
        
        # Add slight randomization to capture region to avoid detection
        self._randomize_capture_region()
        
    def _randomize_capture_region(self) -> None:
        """Add slight randomization to capture region."""
        if random.random() < 0.1:  # 10% chance to adjust region
            offset_x = random.randint(-5, 5)
            offset_y = random.randint(-5, 5)
            
            self.capture_region["left"] = max(0, self.capture_region["left"] + offset_x)
            self.capture_region["top"] = max(0, self.capture_region["top"] + offset_y)
            
    def _setup_color_detection(self) -> None:
        """Setup color detection parameters."""
        self.color_lower = np.array(self.config.color_range_lower)
        self.color_upper = np.array(self.config.color_range_upper)
        
        # Adaptive color range adjustment
        self.color_tolerance = 5
        
    def analyze_frame(self, compensation_offset: float = 0) -> Tuple[Optional[Tuple], bool]:
        """Analyze current frame for target detection."""
        try:
            # Capture screen region
            frame_data = self.screen_capture.grab(self.capture_region)
            
            # Convert to OpenCV format
            frame = cv2.cvtColor(np.array(frame_data), cv2.COLOR_BGRA2BGR)
            
            # Apply preprocessing
            processed_frame = self._preprocess_frame(frame)
            
            # Detect targets
            target_info, action_trigger = self._detect_targets(processed_frame, compensation_offset)
            
            # Update detection history
            self._update_detection_history(target_info)
            
            # Apply stability filtering
            stable_target = self._get_stable_target()
            
            # Handle debug visualization
            if self.config.debug:
                self._show_debug_visualization(frame, processed_frame, stable_target, action_trigger)
                
            return stable_target, action_trigger
            
        except Exception as e:
            print(f"Error in frame analysis: {e}")
            return None, False
            
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for better target detection."""
        # Convert to HSV color space
        hsv_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Apply adaptive color range
        lower_adaptive = np.maximum(self.color_lower - self.color_tolerance, 0)
        upper_adaptive = np.minimum(self.color_upper + self.color_tolerance, 255)
        
        # Create mask
        mask = cv2.inRange(hsv_frame, lower_adaptive, upper_adaptive)
        
        # Invert mask (assuming we want to detect non-matching areas)
        mask = cv2.bitwise_not(mask)
        
        # Apply morphological operations for noise reduction
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        return mask
        
    def _detect_targets(self, processed_frame: np.ndarray, compensation_offset: float) -> Tuple[Optional[Tuple], bool]:
        """Detect targets in the processed frame."""
        # Find contours
        contours, _ = cv2.findContours(processed_frame, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None, False
            
        # Filter contours by area
        min_area = 50  # Minimum target area
        valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
        
        if not valid_contours:
            return None, False
            
        # Find the best target (largest area)
        best_contour = max(valid_contours, key=cv2.contourArea)
        
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(best_contour)
        
        # Calculate target center with compensation
        target_center_x = x + w / 2
        target_center_y = y + h * self.config.target_height_ratio + compensation_offset
        
        # Check if target is in action trigger zone
        action_trigger = self._check_action_trigger(x, y, w, h)
        
        # Add slight randomization to target position for natural behavior
        if random.random() < 0.3:  # 30% chance
            jitter_x, jitter_y = self.behavior_randomizer.simulate_hand_tremor(0.5)
            target_center_x += jitter_x
            target_center_y += jitter_y
            
        target_info = (target_center_x, target_center_y, w, h)
        
        return target_info, action_trigger
        
    def _check_action_trigger(self, x: int, y: int, w: int, h: int) -> bool:
        """Check if target is in the action trigger zone."""
        center_x = self.config.processing_region_x / 2
        center_y = self.config.processing_region_y / 2
        
        # Check if target overlaps with center region
        return (x < center_x < x + w) and (y < center_y < y + h)
        
    def _update_detection_history(self, target_info: Optional[Tuple]) -> None:
        """Update detection history for stability analysis."""
        self.detection_history.append(target_info)
        
        if len(self.detection_history) > self.max_history_length:
            self.detection_history.pop(0)
            
    def _get_stable_target(self) -> Optional[Tuple]:
        """Get stable target based on detection history."""
        if not self.detection_history:
            return None
            
        # Filter out None values
        valid_detections = [d for d in self.detection_history if d is not None]
        
        if len(valid_detections) < 2:
            return self.detection_history[-1]  # Return latest detection
            
        # Calculate average position for stability
        positions = np.array([(d[0], d[1]) for d in valid_detections])
        avg_position = np.mean(positions, axis=0)
        
        # Use dimensions from latest detection
        latest_detection = valid_detections[-1]
        
        return (avg_position[0], avg_position[1], latest_detection[2], latest_detection[3])
        
    def _show_debug_visualization(self, original_frame: np.ndarray, processed_frame: np.ndarray, 
                                 target_info: Optional[Tuple], action_trigger: bool) -> None:
        """Show debug visualization."""
        try:
            if self.config.display_mode == 'mask':
                display_frame = processed_frame
                if target_info:
                    x, y, w, h = target_info
                    cv2.circle(display_frame, (int(x), int(y)), 5, (200, 200, 200), -1)
                    cv2.rectangle(display_frame, (int(x-w/2), int(y-h/2)), 
                                (int(x+w/2), int(y+h/2)), (200, 200, 200), 2)
                cv2.imshow("Visual Processor - Mask", display_frame)
            else:
                display_frame = original_frame.copy()
                if target_info:
                    x, y, w, h = target_info
                    color = (0, 255, 0) if action_trigger else (0, 0, 255)
                    cv2.circle(display_frame, (int(x), int(y)), 5, color, -1)
                    cv2.rectangle(display_frame, (int(x-w/2), int(y-h/2)), 
                                (int(x+w/2), int(y+h/2)), color, 2)
                cv2.imshow("Visual Processor - Game", display_frame)
                
            cv2.waitKey(1)
            
        except Exception as e:
            print(f"Error in debug visualization: {e}")
            
    def __del__(self):
        """Cleanup resources."""
        try:
            cv2.destroyAllWindows()
        except:
            pass
