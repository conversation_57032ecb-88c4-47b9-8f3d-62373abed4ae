# DisplayHelper - Advanced Visual Processing System

## Overview

DisplayHelper is an advanced visual processing and input management system designed for enhanced user interaction. This is a completely refactored version with improved architecture, enhanced security, and better performance.

## Key Improvements

### 🔒 Enhanced Security & Stealth
- **Renamed Components**: All sensitive identifiers have been replaced with neutral terms
- **Behavior Randomization**: Advanced human-like behavior simulation
- **Dynamic Patterns**: Randomized timing and movement patterns to avoid detection
- **Improved Architecture**: Modular design with better separation of concerns

### 🚀 Performance Optimizations
- **Memory Management**: Automatic garbage collection and resource cleanup
- **Frame Rate Control**: Adaptive frame rate management for low-end systems
- **Error Recovery**: Robust error handling with automatic recovery
- **Performance Monitoring**: Real-time performance tracking and warnings

### 🎯 Advanced Features
- **Human-like Movement**: Natural cursor movement with jitter and curves
- **Adaptive Timing**: Dynamic reaction times based on normal distribution
- **Multiple Input Methods**: Support for WinAPI, Interception, Serial, and Socket
- **Visual Processing**: Enhanced target detection with stability filtering

## Architecture

### Core Components

1. **DisplayHelper** (`display_helper.py`)
   - Main system controller
   - Manages component lifecycle
   - Handles error recovery

2. **MovementController** (`movement_controller.py`)
   - Advanced cursor movement calculation
   - Compensation and prediction algorithms
   - Movement history tracking

3. **VisualProcessor** (`visual_processor.py`)
   - Screen capture and analysis
   - Target detection and tracking
   - Debug visualization

4. **SystemManager** (`system_manager.py`)
   - Configuration management
   - Input state monitoring
   - Key binding handling

5. **BehaviorRandomizer** (`behavior_randomizer.py`)
   - Human behavior simulation
   - Randomization algorithms
   - Natural timing generation

6. **Input Handlers** (`mouse/`)
   - Multiple input implementation methods
   - Hardware-level input simulation
   - Network-based input support

## Configuration

The system uses `settings.ini` for configuration with neutral parameter names:

```ini
[enhancement]
input_method = winapi
movement_smoothing_factor = 0.0
movement_speed = 1.0

[visual]
capture_region_x = 256
capture_region_y = 256
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150

[keys]
key_toggle_enhancement = 0x71  # F2
key_toggle_compensation = 0x72  # F3
```

## Usage

### Quick Start
```bash
python start.py
```

### Manual Start
```bash
cd src
python main.py
```

### Default Controls
- **F1**: Reload configuration
- **F2**: Toggle enhancement mode
- **F3**: Toggle compensation mode
- **F4**: Exit system
- **Mouse 4**: Auto action trigger
- **Mouse 5**: Rapid input mode

## Input Methods

### 1. WinAPI (Default)
- Uses Windows API for input simulation
- Good compatibility, moderate stealth

### 2. Interception Driver
- Low-level input simulation
- Requires Interception driver installation
- Higher stealth level

### 3. Microcontroller Serial
- Hardware-based input via Arduino/similar
- Highest stealth level
- Requires microcontroller setup

### 4. Microcontroller Socket
- Network-based microcontroller input
- Good for remote setups
- High stealth level

## Advanced Features

### Human Behavior Simulation
- **Reaction Time**: Normal distribution (150ms ± 50ms)
- **Movement Jitter**: Natural hand tremor simulation
- **Click Timing**: Variable press duration (40-120ms)
- **Fatigue Modeling**: Slower reactions over time

### Performance Optimization
- **Adaptive Quality**: Dynamic processing quality based on performance
- **Memory Management**: Automatic cleanup every 30 seconds
- **Error Recovery**: Exponential backoff on consecutive errors
- **Frame Rate Control**: Maintains consistent timing

### Debug Features
- **Visual Overlay**: Real-time target visualization
- **Performance Metrics**: Frame time monitoring
- **Console Logging**: Detailed operation logging
- **Statistics**: Movement and detection statistics

## System Requirements

- **OS**: Windows 10/11
- **Python**: 3.8+
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Any modern processor
- **Dependencies**: OpenCV, NumPy, MSS, PyWin32

## Installation

1. **Install Dependencies**:
   ```bash
   pip install opencv-python numpy mss pywin32
   ```

2. **Optional Dependencies**:
   ```bash
   # For Interception driver support
   pip install interception
   
   # For Serial communication
   pip install pyserial
   ```

3. **Run the System**:
   ```bash
   python start.py
   ```

## Security Considerations

This refactored version includes numerous improvements to reduce detection risk:

- **Code Obfuscation**: Neutral naming throughout
- **Behavioral Mimicry**: Human-like patterns
- **Dynamic Adaptation**: Changing patterns over time
- **Error Resilience**: Graceful failure handling
- **Resource Management**: Minimal system footprint

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Permission Issues**: Run as administrator if needed
3. **Performance Issues**: Adjust frame rate in settings
4. **Detection Issues**: Calibrate color ranges in settings

### Performance Tuning

- Lower `max_frames_per_sec` for better performance
- Reduce `capture_region_x/y` for faster processing
- Disable debug mode for production use
- Use hardware input methods for better performance

## License

This program is free software under the GNU General Public License v3.0.
See LICENSE.txt for details.

## Disclaimer

This software is for educational and research purposes only. Users are responsible for compliance with applicable laws and terms of service.
