# DisplayHelper 快速参考

## 🚀 快速启动

```bash
# 启动系统
python start.py

# 或者
cd src && python main.py
```

## ⌨️ 默认控制键

| 按键 | 功能 | 说明 |
|------|------|------|
| **F1** | 重载配置文件 | 修改配置后按此键生效 |
| **F2** | 自瞄开关 | 切换自瞄功能开/关 |
| **F3** | 压枪开关 | 切换压枪功能开/关 |
| **F4** | 退出系统 | 安全退出程序 |
| **鼠标4** | 扳机键（按住） | 按住时自动开火 |
| **鼠标5** | 连点键（按住） | 按住时快速射击 |
| **鼠标1+2** | 自瞄激活键 | 按住时自瞄工作 |

## 🎯 功能说明速查

### 自瞄功能
```ini
[enhancement]
# 启用/关闭自瞄
key_toggle_enhancement = 0x71           # F2键切换自瞄开关

# 自瞄激活按键（按住时自瞄工作）
enhancement_activation_keys = 0x01, 0x02  # 鼠标左键+右键
# enhancement_activation_keys = 0x06     # 鼠标侧键4
# enhancement_activation_keys = off      # 无需按键，始终激活

# 自瞄速度和平滑度
movement_speed = 1.0                    # 自瞄速度 (0.1-5.0)
movement_smoothing_factor = 0.3         # 自瞄平滑度 (0.0-1.0)
target_height_ratio = 0.5               # 瞄准位置 (0.0头部-1.0脚部)
```

### 自瞄范围设置
```ini
[visual]
# 自瞄检测范围
capture_region_x = 256                  # 检测区域宽度
capture_region_y = 256                  # 检测区域高度

# 目标颜色识别（HSV格式）
color_range_upper = 115, 255, 255       # 颜色上限
color_range_lower = 95, 150, 150        # 颜色下限

# 自瞄性能
max_frames_per_sec = 60                 # 检测帧率
```

### 扳机功能
```ini
[keys]
# 扳机按键（按住时自动开火）
key_auto_action = 0x06                  # 鼠标侧键4

[action]
# 扳机延迟设置
action_delay = 25                       # 基础延迟(ms)
action_randomization = 30               # 随机延迟范围(ms)
action_threshold = 8                    # 扳机触发阈值
```

### 压枪功能
```ini
[keys]
# 压枪开关
key_toggle_compensation = 0x72          # F3键切换压枪

[compensation]
# 压枪参数
mode = move                             # 压枪模式
compensation_y = 15.0                   # 垂直压枪力度
compensation_x = 0.0                    # 水平压枪力度
recovery_rate = 50.0                    # 压枪恢复速度
```

### 快速射击
```ini
[keys]
# 快速射击按键
key_rapid_input = 0x05                  # 鼠标侧键5

[input_rate]
# 射击频率限制
target_rate = 10                        # 每秒最大点击次数
```

### 调试模式
```ini
[debug]
# 显示自瞄框和检测区域
enabled = true                          # 启用调试显示
always_on = true                        # 始终显示（即使自瞄关闭）
display_mode = mask                     # 显示模式 (mask/game)
```

## ⚙️ 常用配置速查

### 基础设置

```ini
[enhancement]
input_method = winapi                    # 输入方法
movement_smoothing_factor = 0.3         # 平滑度 (0.0-1.0)
movement_speed = 1.0                    # 移动速度
target_height_ratio = 0.5               # 瞄准高度 (0.0-1.0)

[visual]
capture_region_x = 256                  # 捕获宽度
capture_region_y = 256                  # 捕获高度
max_frames_per_sec = 60                 # 最大帧率
```

### 颜色配置（HSV）

```ini
[visual]
# 红色
color_range_upper = 10, 255, 255
color_range_lower = 0, 120, 70

# 蓝色
color_range_upper = 130, 255, 255
color_range_lower = 100, 120, 70

# 绿色
color_range_upper = 80, 255, 255
color_range_lower = 40, 120, 70
```

## 🎯 预设配置

### 竞技模式（高性能）

```ini
# 快速响应，适合竞技游戏
[enhancement]
input_method = interception_driver      # 更安全的输入方式
movement_smoothing_factor = 0.1         # 低平滑，快速响应
movement_speed = 1.2                    # 高自瞄速度

[visual]
capture_region_x = 384                  # 大检测范围
capture_region_y = 384
max_frames_per_sec = 120                # 高帧率

[action]
action_delay = 15                       # 快速扳机
action_randomization = 20
```

### 休闲模式（平衡）

```ini
# 平衡性能和稳定性
[enhancement]
movement_smoothing_factor = 0.5         # 中等平滑
movement_speed = 0.8                    # 中等自瞄速度

[visual]
capture_region_x = 128                  # 小检测范围，省性能
capture_region_y = 128
max_frames_per_sec = 30                 # 低帧率

[action]
action_delay = 50                       # 保守扳机延迟
action_randomization = 40
```

### 调试模式

```ini
# 用于调试和设置
[debug]
enabled = true                          # 显示自瞄框
always_on = true                        # 始终显示
display_mode = mask                     # 显示检测遮罩

[visual]
max_frames_per_sec = 30                 # 降低性能消耗
```

## 🔧 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 自瞄不工作 | 1.按F2开启自瞄 2.按住激活键 3.调整颜色范围 |
| 检测不到敌人 | 启用调试模式，调整HSV颜色范围 |
| 自瞄太慢 | 降低平滑度到0.1-0.2，提高移动速度 |
| 自瞄太快/抖动 | 增加平滑度到0.3-0.5，降低移动速度 |
| 扳机不开火 | 检查扳机按键设置，调整触发阈值 |
| 性能卡顿 | 降低帧率，减小检测区域 |
| 程序崩溃 | 检查依赖包，以管理员身份运行 |

### 性能优化

```ini
# CPU 使用率过高
[visual]
max_frames_per_sec = 30
capture_region_x = 128
capture_region_y = 128

# 内存使用过高
[debug]
enabled = false
```

## 📱 输入方法对比

| 方法 | 兼容性 | 安全性 | 复杂度 | 推荐场景 |
|------|--------|--------|--------|----------|
| **winapi** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ | 一般用户 |
| **interception** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 高级用户 |
| **serial** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 专业用户 |
| **socket** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 远程控制 |

## 🎨 颜色调试技巧

### HSV 调试步骤

1. **启用调试模式**
   ```ini
   [debug]
   enabled = true
   display_mode = mask
   ```

2. **观察遮罩窗口**
   - 白色区域：检测到的目标
   - 黑色区域：被过滤的背景

3. **调整颜色范围**
   ```ini
   # 目标太暗 → 降低 V 下限
   color_range_lower = 95, 150, 100  # 原来 150
   
   # 目标太亮 → 提高 V 下限
   color_range_lower = 95, 150, 200  # 原来 150
   
   # 范围太窄 → 扩大 H 范围
   color_range_upper = 125, 255, 255  # 原来 115
   color_range_lower = 85, 150, 150   # 原来 95
   ```

## 🔑 虚拟键码速查

### 常用按键

```ini
# 功能键
F1=0x70  F2=0x71  F3=0x72  F4=0x73
F5=0x74  F6=0x75  F7=0x76  F8=0x77

# 鼠标
左键=0x01  右键=0x02  中键=0x04
侧键4=0x05  侧键5=0x06

# 字母
A=0x41  Q=0x51  W=0x57  E=0x45
R=0x52  T=0x54  Y=0x59  U=0x55

# 数字
1=0x31  2=0x32  3=0x33  4=0x34
5=0x35  6=0x36  7=0x37  8=0x38

# 特殊
空格=0x20  Shift=0x10  Ctrl=0x11  Alt=0x12
```

## 📊 参数范围速查

| 参数 | 范围 | 推荐值 | 功能说明 |
|------|------|--------|----------|
| `movement_smoothing_factor` | 0.0-1.0 | 0.3 | 自瞄平滑度（0=最快，1=最稳） |
| `movement_speed` | 0.1-5.0 | 1.0 | 自瞄速度倍数 |
| `target_height_ratio` | 0.0-1.0 | 0.5 | 瞄准位置（0=头部，1=脚部） |
| `capture_region_x/y` | 64-1024 | 256 | 自瞄检测范围大小 |
| `max_frames_per_sec` | 15-240 | 60 | 检测帧率（影响性能） |
| `action_delay` | 0-500 | 25 | 扳机延迟（毫秒） |
| `action_randomization` | 0-200 | 30 | 扳机随机延迟范围 |
| `compensation_y` | 0-100 | 15 | 压枪力度（垂直方向） |

## 🚨 安全使用提醒

### ✅ 推荐做法

- 从保守配置开始
- 定期备份配置文件
- 在安全环境中测试
- 关注系统性能警告

### ❌ 避免做法

- 使用过于激进的参数
- 忽略性能警告
- 在不熟悉环境中使用
- 长时间连续使用

## 📞 获取帮助

1. 查看 `USER_GUIDE.md` 获取详细说明
2. 查看 `CONFIGURATION_MANUAL.md` 获取配置详情
3. 查看 `README_REFACTORED.md` 获取系统概述
4. 检查控制台输出获取错误信息

---

**提示：** 配置修改后按 F1 重载配置，无需重启程序。
