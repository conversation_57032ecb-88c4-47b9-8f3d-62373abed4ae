# DisplayHelper 快速参考

## 🚀 快速启动

```bash
# 启动系统
python start.py

# 或者
cd src && python main.py
```

## ⌨️ 默认控制键

| 按键 | 功能 |
|------|------|
| **F1** | 重载配置文件 |
| **F2** | 切换增强模式 |
| **F3** | 切换补偿模式 |
| **F4** | 退出系统 |
| **鼠标4** | 自动动作（按住） |
| **鼠标5** | 快速输入（按住） |
| **鼠标1+2** | 激活增强功能 |

## ⚙️ 常用配置速查

### 基础设置

```ini
[enhancement]
input_method = winapi                    # 输入方法
movement_smoothing_factor = 0.3         # 平滑度 (0.0-1.0)
movement_speed = 1.0                    # 移动速度
target_height_ratio = 0.5               # 瞄准高度 (0.0-1.0)

[visual]
capture_region_x = 256                  # 捕获宽度
capture_region_y = 256                  # 捕获高度
max_frames_per_sec = 60                 # 最大帧率
```

### 颜色配置（HSV）

```ini
[visual]
# 红色
color_range_upper = 10, 255, 255
color_range_lower = 0, 120, 70

# 蓝色
color_range_upper = 130, 255, 255
color_range_lower = 100, 120, 70

# 绿色
color_range_upper = 80, 255, 255
color_range_lower = 40, 120, 70
```

## 🎯 预设配置

### 高性能模式

```ini
[enhancement]
input_method = interception_driver
movement_smoothing_factor = 0.1
movement_speed = 1.2

[visual]
capture_region_x = 384
capture_region_y = 384
max_frames_per_sec = 120

[action]
action_delay = 15
action_randomization = 20
```

### 节能模式

```ini
[enhancement]
movement_smoothing_factor = 0.5
movement_speed = 0.8

[visual]
capture_region_x = 128
capture_region_y = 128
max_frames_per_sec = 30

[action]
action_delay = 50
action_randomization = 40
```

### 调试模式

```ini
[debug]
enabled = true
always_on = true
display_mode = mask

[visual]
max_frames_per_sec = 30
```

## 🔧 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 检测不到目标 | 调整颜色范围，启用调试模式 |
| 性能过低 | 降低帧率，减小捕获区域 |
| 移动不平滑 | 增加平滑因子到 0.3-0.5 |
| 响应太慢 | 减少平滑因子到 0.1-0.2 |
| 程序崩溃 | 检查依赖包，以管理员身份运行 |

### 性能优化

```ini
# CPU 使用率过高
[visual]
max_frames_per_sec = 30
capture_region_x = 128
capture_region_y = 128

# 内存使用过高
[debug]
enabled = false
```

## 📱 输入方法对比

| 方法 | 兼容性 | 安全性 | 复杂度 | 推荐场景 |
|------|--------|--------|--------|----------|
| **winapi** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ | 一般用户 |
| **interception** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 高级用户 |
| **serial** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 专业用户 |
| **socket** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 远程控制 |

## 🎨 颜色调试技巧

### HSV 调试步骤

1. **启用调试模式**
   ```ini
   [debug]
   enabled = true
   display_mode = mask
   ```

2. **观察遮罩窗口**
   - 白色区域：检测到的目标
   - 黑色区域：被过滤的背景

3. **调整颜色范围**
   ```ini
   # 目标太暗 → 降低 V 下限
   color_range_lower = 95, 150, 100  # 原来 150
   
   # 目标太亮 → 提高 V 下限
   color_range_lower = 95, 150, 200  # 原来 150
   
   # 范围太窄 → 扩大 H 范围
   color_range_upper = 125, 255, 255  # 原来 115
   color_range_lower = 85, 150, 150   # 原来 95
   ```

## 🔑 虚拟键码速查

### 常用按键

```ini
# 功能键
F1=0x70  F2=0x71  F3=0x72  F4=0x73
F5=0x74  F6=0x75  F7=0x76  F8=0x77

# 鼠标
左键=0x01  右键=0x02  中键=0x04
侧键4=0x05  侧键5=0x06

# 字母
A=0x41  Q=0x51  W=0x57  E=0x45
R=0x52  T=0x54  Y=0x59  U=0x55

# 数字
1=0x31  2=0x32  3=0x33  4=0x34
5=0x35  6=0x36  7=0x37  8=0x38

# 特殊
空格=0x20  Shift=0x10  Ctrl=0x11  Alt=0x12
```

## 📊 参数范围速查

| 参数 | 范围 | 推荐值 | 说明 |
|------|------|--------|------|
| `movement_smoothing_factor` | 0.0-1.0 | 0.3 | 平滑度 |
| `movement_speed` | 0.1-5.0 | 1.0 | 移动速度 |
| `target_height_ratio` | 0.0-1.0 | 0.5 | 瞄准位置 |
| `capture_region_x/y` | 64-1024 | 256 | 捕获区域 |
| `max_frames_per_sec` | 15-240 | 60 | 帧率限制 |
| `action_delay` | 0-500 | 25 | 动作延迟(ms) |
| `action_randomization` | 0-200 | 30 | 随机延迟(ms) |

## 🚨 安全使用提醒

### ✅ 推荐做法

- 从保守配置开始
- 定期备份配置文件
- 在安全环境中测试
- 关注系统性能警告

### ❌ 避免做法

- 使用过于激进的参数
- 忽略性能警告
- 在不熟悉环境中使用
- 长时间连续使用

## 📞 获取帮助

1. 查看 `USER_GUIDE.md` 获取详细说明
2. 查看 `CONFIGURATION_MANUAL.md` 获取配置详情
3. 查看 `README_REFACTORED.md` 获取系统概述
4. 检查控制台输出获取错误信息

---

**提示：** 配置修改后按 F1 重载配置，无需重启程序。
