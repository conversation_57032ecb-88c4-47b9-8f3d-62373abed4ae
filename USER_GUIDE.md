# DisplayHelper 用户指南

## 📖 目录

1. [系统概述](#系统概述)
2. [安装与环境配置](#安装与环境配置)
3. [配置文件详解](#配置文件详解)
4. [使用教程](#使用教程)
5. [高级配置指南](#高级配置指南)
6. [技术说明](#技术说明)
7. [故障排除](#故障排除)

---

## 🎯 系统概述

DisplayHelper 是一个先进的视觉处理和输入管理系统，具有以下核心特性：

- **智能视觉处理**：实时屏幕分析和目标识别
- **人性化行为模拟**：自然的鼠标移动和点击模式
- **多种输入方法**：支持 WinAPI、Interception、串行和网络输入
- **高度可配置**：丰富的参数调整选项
- **性能优化**：适配低性能设备的优化机制

---

## 🔧 安装与环境配置

### 系统要求

- **操作系统**：Windows 10/11 (64位)
- **Python版本**：3.8 或更高版本
- **内存**：最少 4GB，推荐 8GB
- **处理器**：任何现代处理器
- **显卡**：支持 DirectX 11 的显卡

### 安装步骤

#### 1. 安装 Python 依赖

```bash
# 基础依赖（必需）
pip install opencv-python numpy mss pywin32

# 可选依赖
pip install interception    # Interception 驱动支持
pip install pyserial       # 串行通信支持
```

#### 2. 验证安装

```bash
# 检查 Python 版本
python --version

# 验证依赖包
python -c "import cv2, numpy, mss, win32api; print('所有依赖已正确安装')"
```

#### 3. 首次运行

```bash
# 使用启动脚本（推荐）
python start.py

# 或直接运行
cd src
python main.py
```

---

## ⚙️ 配置文件详解

### settings.ini 文件结构

配置文件采用 INI 格式，包含以下主要节：

#### [enhancement] - 增强功能设置

```ini
[enhancement]
input_method = winapi                    # 输入方法选择
display_center_offset = 0               # 显示中心偏移
movement_smoothing_factor = 0.0         # 移动平滑因子
movement_speed = 1.0                    # 移动速度倍数
vertical_speed_multiplier = 1.0         # 垂直速度倍数
target_height_ratio = 0.5               # 目标高度比例
```

**参数详解：**

- **input_method**：输入方法选择
  - `winapi`：Windows API（默认，兼容性最好）
  - `interception_driver`：Interception 驱动（需要额外安装）
  - `microcontroller_serial`：串行微控制器
  - `microcontroller_socket`：网络微控制器

- **movement_smoothing_factor**：移动平滑度
  - 范围：0.0 - 1.0
  - 0.0：无平滑（最快响应）
  - 1.0：最大平滑（最稳定）
  - 推荐：0.2 - 0.4

- **movement_speed**：移动速度
  - 范围：0.1 - 5.0
  - 1.0：标准速度
  - 推荐：0.8 - 1.5

#### [device] - 设备通信设置

```ini
[device]
device_ip = *************               # 设备IP地址
device_port = 50256                     # 网络端口
serial_port = COM3                      # 串行端口
```

#### [visual] - 视觉处理设置

```ini
[visual]
target_grouping_threshold = 3, 3        # 目标分组阈值
color_range_upper = 115, 255, 255       # HSV颜色上限
color_range_lower = 95, 150, 150        # HSV颜色下限
capture_region_x = 256                  # 捕获区域宽度
capture_region_y = 256                  # 捕获区域高度
processing_region_x = 256               # 处理区域宽度
processing_region_y = 256               # 处理区域高度
max_frames_per_sec = 60                 # 最大帧率
auto_detect_resolution = true           # 自动检测分辨率
resolution_x = 1920                     # 手动设置分辨率X
resolution_y = 1080                     # 手动设置分辨率Y
```

**颜色配置说明：**

HSV 颜色空间配置用于目标识别：
- **H (色调)**：0-179
- **S (饱和度)**：0-255  
- **V (亮度)**：0-255

常用颜色范围：
```ini
# 红色目标
color_range_upper = 10, 255, 255
color_range_lower = 0, 120, 70

# 蓝色目标  
color_range_upper = 130, 255, 255
color_range_lower = 100, 120, 70

# 绿色目标
color_range_upper = 80, 255, 255
color_range_lower = 40, 120, 70
```

#### [compensation] - 补偿设置

```ini
[compensation]
mode = move                             # 补偿模式
compensation_x = 0.0                    # X轴补偿值
compensation_y = 15.0                   # Y轴补偿值
max_offset = 100                        # 最大偏移量
recovery_rate = 50.0                    # 恢复速率
```

**补偿模式：**
- `move`：直接移动补偿
- `offset`：偏移补偿

#### [action] - 动作设置

```ini
[action]
action_delay = 0                        # 基础延迟（毫秒）
action_randomization = 30               # 随机延迟范围
action_threshold = 8                    # 动作阈值
```

#### [input_rate] - 输入频率设置

```ini
[input_rate]
target_rate = 10                        # 目标输入频率（每秒）
```

#### [keys] - 按键绑定

```ini
[keys]
key_reload_settings = 0x70              # F1 - 重载配置
key_toggle_enhancement = 0x71           # F2 - 切换增强
key_toggle_compensation = 0x72          # F3 - 切换补偿
key_system_exit = 0x73                  # F4 - 退出系统
key_auto_action = 0x06                  # 鼠标4 - 自动动作
key_rapid_input = 0x05                  # 鼠标5 - 快速输入
enhancement_activation_keys = 0x01, 0x02 # 鼠标1&2 - 激活键
```

**虚拟键码参考：**
- F1-F12：0x70-0x7B
- 鼠标左键：0x01
- 鼠标右键：0x02
- 鼠标中键：0x04
- 鼠标侧键4：0x05
- 鼠标侧键5：0x06

#### [debug] - 调试设置

```ini
[debug]
enabled = false                         # 启用调试模式
always_on = false                       # 始终显示调试
display_mode = mask                     # 显示模式
```

**显示模式：**
- `game`：显示原始画面
- `mask`：显示处理后的遮罩

---

## 📚 使用教程

### 基本操作流程

#### 1. 启动系统

```bash
# 方法1：使用启动脚本
python start.py

# 方法2：直接运行
cd src && python main.py
```

#### 2. 系统状态指示

启动后会显示：
```
DisplayHelper System  Copyright (C) 2025  Development Team
This program comes with ABSOLUTELY NO WARRANTY.
This is free software, and you are welcome to redistribute it under certain conditions.
For details see <LICENSE.txt>.
Using WinAPI Input Handler
System Active
```

#### 3. 基本控制

| 按键 | 功能 | 说明 |
|------|------|------|
| F1 | 重载配置 | 重新读取 settings.ini |
| F2 | 切换增强模式 | 开启/关闭主要功能 |
| F3 | 切换补偿模式 | 开启/关闭移动补偿 |
| F4 | 退出系统 | 安全退出程序 |
| 鼠标4 | 自动动作 | 按住时启用自动动作 |
| 鼠标5 | 快速输入 | 按住时启用快速输入 |

### 不同输入方法配置

#### WinAPI 方法（默认）

```ini
[enhancement]
input_method = winapi
```

**特点：**
- 兼容性最好
- 无需额外驱动
- 适合大多数用户

#### Interception 驱动方法

```ini
[enhancement]
input_method = interception_driver
```

**安装步骤：**
1. 下载 Interception 驱动
2. 安装驱动程序
3. 安装 Python 包：`pip install interception`

**特点：**
- 更低级的输入模拟
- 更好的兼容性
- 需要管理员权限

#### 串行微控制器方法

```ini
[enhancement]
input_method = microcontroller_serial

[device]
serial_port = COM3
```

**硬件要求：**
- Arduino 或兼容微控制器
- USB 连接线
- 预装固件（见 `src/microcontroller/serial.ino`）

#### 网络微控制器方法

```ini
[enhancement]
input_method = microcontroller_socket

[device]
device_ip = *************
device_port = 50256
```

**网络要求：**
- 支持 WiFi 的微控制器
- 局域网连接
- 预装固件（见 `src/microcontroller/wifi.ino`）

### 调试模式使用

#### 启用调试

```ini
[debug]
enabled = true
always_on = true
display_mode = mask
```

#### 调试窗口说明

- **Mask 模式**：显示处理后的黑白图像
- **Game 模式**：在原始画面上叠加标记
- **绿色圆圈**：检测到的目标中心
- **红色矩形**：目标边界框
- **颜色变化**：表示不同的检测状态

---

## 🚀 高级配置指南

### 性能优化配置

#### 低性能设备优化

```ini
[visual]
capture_region_x = 128          # 减小捕获区域
capture_region_y = 128
max_frames_per_sec = 30         # 降低帧率

[enhancement]
movement_smoothing_factor = 0.5  # 增加平滑度减少计算
```

#### 高性能设备配置

```ini
[visual]
capture_region_x = 512          # 增大捕获区域
capture_region_y = 512
max_frames_per_sec = 120        # 提高帧率

[enhancement]
movement_smoothing_factor = 0.1  # 减少平滑度提高响应
```

### 不同场景配置

#### 精确模式（高精度要求）

```ini
[enhancement]
movement_speed = 0.8
movement_smoothing_factor = 0.3

[action]
action_delay = 50
action_randomization = 20
```

#### 快速模式（高响应要求）

```ini
[enhancement]
movement_speed = 1.5
movement_smoothing_factor = 0.1

[action]
action_delay = 0
action_randomization = 10
```

### 颜色校准指南

#### 使用调试模式校准

1. 启用调试模式
2. 设置 `display_mode = mask`
3. 观察目标在遮罩中的显示
4. 调整 HSV 颜色范围直到目标清晰可见

#### HSV 调整技巧

```ini
# 目标太暗 - 降低 V 下限
color_range_lower = 95, 150, 100  # 原来是 150

# 目标太亮 - 提高 V 下限  
color_range_lower = 95, 150, 200  # 原来是 150

# 颜色范围太窄 - 扩大 H 范围
color_range_upper = 125, 255, 255  # 原来是 115
color_range_lower = 85, 150, 150   # 原来是 95
```

---

## 🔬 技术说明

### 行为随机化系统

系统使用多层随机化来模拟自然行为：

#### 反应时间模拟
- 基于正态分布：150ms ± 50ms
- 疲劳因子：长时间使用后反应变慢
- 范围限制：50ms - 500ms

#### 鼠标移动随机化
- **基础抖动**：正态分布噪声
- **微调修正**：15% 概率的小幅反向移动
- **轻微超调**：8% 概率的轻微过度移动
- **手部颤抖**：6-15Hz 的正弦波模拟

#### 点击时序随机化
- **按压时长**：40-120ms 的伽马分布
- **释放延迟**：25-60ms 的变化
- **动作间隔**：指数分布的自然间隔

### 视觉处理系统

#### 多级处理流程
1. **屏幕捕获**：使用 MSS 库高效截图
2. **颜色空间转换**：BGR → HSV
3. **颜色过滤**：基于配置的 HSV 范围
4. **形态学处理**：噪声去除和形状优化
5. **轮廓检测**：查找目标边界
6. **稳定性过滤**：基于历史数据的稳定化

#### 自适应机制
- **动态颜色范围**：根据环境光线调整
- **区域随机化**：10% 概率微调捕获区域
- **性能自适应**：根据帧率自动调整质量

### 输入管理系统

#### 精确移动计算
- **余数处理**：亚像素级精度保持
- **移动预测**：基于历史数据的轨迹预测
- **平滑算法**：自适应平滑因子

#### 多线程架构
- **主处理线程**：视觉分析和逻辑控制
- **输入线程**：独立的鼠标和键盘处理
- **清理线程**：定期内存和资源清理

---

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 程序无法启动

**错误信息：** `ImportError: No module named 'cv2'`

**解决方案：**
```bash
pip install opencv-python
```

**错误信息：** `ImportError: No module named 'win32api'`

**解决方案：**
```bash
pip install pywin32
```

#### 2. 检测不到目标

**可能原因：**
- 颜色配置不正确
- 捕获区域设置错误
- 目标太小或太大

**解决步骤：**
1. 启用调试模式
2. 检查遮罩显示
3. 调整 HSV 颜色范围
4. 调整捕获区域大小

#### 3. 性能问题

**症状：** 帧率过低，系统卡顿

**解决方案：**
```ini
[visual]
max_frames_per_sec = 30         # 降低帧率
capture_region_x = 128          # 减小区域
capture_region_y = 128

[debug]
enabled = false                 # 关闭调试模式
```

#### 4. 输入延迟过高

**解决方案：**
```ini
[enhancement]
movement_smoothing_factor = 0.0  # 减少平滑

[action]
action_delay = 0                # 移除基础延迟
```

#### 5. Interception 驱动问题

**错误信息：** `Interception driver not available`

**解决步骤：**
1. 确认驱动已正确安装
2. 以管理员身份运行程序
3. 检查驱动服务状态

#### 6. 串行通信问题

**错误信息：** `Failed to connect to serial device`

**解决步骤：**
1. 检查 COM 端口号
2. 确认设备连接
3. 检查波特率设置（默认 9600）
4. 验证固件是否正确烧录

### 日志和调试

#### 启用详细日志

```ini
[debug]
enabled = true
```

#### 性能监控

程序会自动监控：
- 帧处理时间
- 内存使用情况
- 错误恢复状态

#### 手动诊断

```bash
# 检查 Python 环境
python -c "import sys; print(sys.version)"

# 验证依赖包
python -c "import cv2, numpy, mss, win32api; print('OK')"

# 测试摄像头访问
python -c "import mss; sct = mss.mss(); print('Screen capture OK')"
```

---

## 📞 技术支持

如果遇到本指南未涵盖的问题，请：

1. 检查 `README_REFACTORED.md` 获取更多信息
2. 查看源代码注释了解技术细节
3. 确保使用最新版本的依赖包

---

## 📋 配置模板

### 基础配置模板

```ini
# DisplayHelper 基础配置模板
# 适合初次使用和一般场景

[enhancement]
input_method = winapi
display_center_offset = 0
movement_smoothing_factor = 0.3
movement_speed = 1.0
vertical_speed_multiplier = 1.0
target_height_ratio = 0.5

[device]
device_ip = *************
device_port = 50256
serial_port = COM3

[visual]
target_grouping_threshold = 3, 3
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150
capture_region_x = 256
capture_region_y = 256
processing_region_x = 256
processing_region_y = 256
max_frames_per_sec = 60
auto_detect_resolution = true
resolution_x = 1920
resolution_y = 1080

[compensation]
mode = move
compensation_x = 0.0
compensation_y = 15.0
max_offset = 100
recovery_rate = 50.0

[action]
action_delay = 25
action_randomization = 30
action_threshold = 8

[input_rate]
target_rate = 10

[keys]
key_reload_settings = 0x70
key_toggle_enhancement = 0x71
key_toggle_compensation = 0x72
key_system_exit = 0x73
key_auto_action = 0x06
key_rapid_input = 0x05
enhancement_activation_keys = 0x01, 0x02

[debug]
enabled = false
always_on = false
display_mode = mask
```

### 高性能配置模板

```ini
# 高性能设备专用配置
# 适合高端硬件和竞技场景

[enhancement]
input_method = interception_driver
movement_smoothing_factor = 0.1
movement_speed = 1.2
vertical_speed_multiplier = 1.1

[visual]
capture_region_x = 384
capture_region_y = 384
max_frames_per_sec = 120

[compensation]
compensation_y = 12.0
recovery_rate = 75.0

[action]
action_delay = 15
action_randomization = 20
```

### 低性能配置模板

```ini
# 低性能设备优化配置
# 适合老旧硬件和节能模式

[enhancement]
movement_smoothing_factor = 0.5
movement_speed = 0.8

[visual]
capture_region_x = 128
capture_region_y = 128
max_frames_per_sec = 30

[action]
action_delay = 50
action_randomization = 40

[debug]
enabled = false
```

## 🎮 微控制器设置指南

### Arduino 硬件配置

#### 所需硬件
- Arduino Leonardo 或 Pro Micro（推荐）
- USB 数据线
- 可选：LED 指示灯

#### 固件烧录步骤

1. **下载 Arduino IDE**
2. **选择开发板**：Tools → Board → Arduino Leonardo
3. **选择端口**：Tools → Port → COM3（根据实际情况）
4. **烧录固件**：
   ```cpp
   // 使用 src/microcontroller/serial.ino
   // 或 src/microcontroller/wifi.ino（WiFi版本）
   ```

#### 串行通信配置

```ini
[enhancement]
input_method = microcontroller_serial

[device]
serial_port = COM3  # 根据设备管理器中的实际端口
```

#### 网络通信配置（ESP32/ESP8266）

```ini
[enhancement]
input_method = microcontroller_socket

[device]
device_ip = *************    # 微控制器的IP地址
device_port = 50256          # 通信端口
```

### 微控制器固件说明

#### 串行版本功能
- 接收移动命令：`M<x>,<y>\r`
- 接收点击命令：`C\r`
- 返回确认信息：`a\r\n`

#### WiFi版本功能
- TCP Socket 通信
- 自动重连机制
- 状态LED指示

## 🔍 高级调试技巧

### 颜色校准工具

#### 手动颜色提取

```python
# 临时脚本：color_picker.py
import cv2
import numpy as np

def pick_color(event, x, y, flags, param):
    if event == cv2.EVENT_LBUTTONDOWN:
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        h, s, v = hsv[y, x]
        print(f"HSV: ({h}, {s}, {v})")

# 使用方法：
# 1. 截取包含目标的图片
# 2. 运行脚本点击目标获取HSV值
# 3. 根据获取的值调整配置
```

#### 实时颜色调试

```ini
[debug]
enabled = true
always_on = true
display_mode = mask

# 观察遮罩窗口，调整以下参数直到目标清晰可见
[visual]
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150
```

### 性能分析工具

#### 帧率监控

程序会自动显示性能警告：
```
Performance warning: Average frame time 45.2ms
```

#### 内存使用监控

系统每30秒自动清理内存，如果频繁出现清理信息，考虑：
- 降低帧率
- 减小捕获区域
- 关闭调试模式

### 网络诊断

#### 测试微控制器连接

```bash
# Windows 命令行测试
telnet ************* 50256

# 如果连接成功，可以手动发送命令：
# M10,10  （移动鼠标）
# C       （点击）
```

#### 串行端口测试

```python
# 临时脚本：serial_test.py
import serial
import time

try:
    ser = serial.Serial('COM3', 9600, timeout=1)
    time.sleep(2)

    # 发送测试命令
    ser.write(b'M5,5\r')
    response = ser.readline()
    print(f"Response: {response}")

    ser.close()
    print("Serial test successful")
except Exception as e:
    print(f"Serial test failed: {e}")
```

## 🛡️ 安全使用建议

### 配置安全

#### 文件权限设置
```bash
# Windows 设置文件只读（可选）
attrib +R settings.ini
```

#### 备份重要配置
```bash
# 创建配置备份
copy settings.ini settings_backup.ini
```

### 使用安全

#### 推荐的使用模式
1. **测试模式**：先在安全环境中测试所有功能
2. **渐进配置**：从保守配置开始，逐步调整
3. **定期检查**：定期验证配置文件的完整性

#### 避免的行为
- 不要在不熟悉的环境中使用
- 不要使用过于激进的配置参数
- 不要忽略系统性能警告

### 隐私保护

#### 数据处理
- 系统不会保存任何屏幕截图
- 不会记录用户操作日志
- 所有处理都在本地进行

#### 网络安全
- 微控制器通信仅限局域网
- 不会向外部服务器发送数据
- 建议使用防火墙限制网络访问

## 📈 性能优化深度指南

### CPU 优化

#### 多核利用
```ini
# 系统会自动利用多核处理器
# 主线程：视觉处理
# 子线程：输入处理、清理任务
```

#### 处理优先级
```ini
[visual]
max_frames_per_sec = 60  # 根据CPU能力调整

# CPU使用率过高时的建议：
# 1. 降低帧率到30-45
# 2. 减小捕获区域
# 3. 关闭调试模式
```

### 内存优化

#### 自动内存管理
- 每30秒自动垃圾回收
- 智能对象生命周期管理
- 历史数据自动清理

#### 手动优化建议
```ini
# 减少内存使用的配置
[visual]
capture_region_x = 128      # 更小的捕获区域
capture_region_y = 128
max_frames_per_sec = 30     # 更低的帧率

# 避免内存泄漏
[debug]
enabled = false             # 生产环境关闭调试
```

### 显卡优化

#### OpenCV 加速
系统自动使用可用的硬件加速：
- Intel IPP 优化
- OpenCL 支持（如果可用）
- CUDA 支持（如果可用）

#### 显示优化
```ini
[debug]
enabled = false    # 关闭调试窗口减少GPU负载
```

**免责声明：** 本软件仅供教育和研究目的使用。用户有责任确保遵守适用的法律法规和服务条款。
